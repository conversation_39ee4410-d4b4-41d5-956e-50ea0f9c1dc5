# Release Procedure

Please note that everyone must participate 
in doing the release. 
It is not only one person job. 
The release manager makes a release 
based on the procedures documented here. 
Release can not happen without 
proper procedures. Every step has to be documented so we can
make consistent releases.
This document must be kept up to date. 
Each time release is made, be sure to review 
and update this doc. 

First documented step is to do all tests. 
The steps are:

- Code freeze. No more PR before doing release
- Run all unit tests and regression test in [tests.md](https://github.com/bbtechhive/mnms/blob/main/doc/tests.md)
- Run all tests listed as [github issues tagged "testing"](https://github.com/bbtechhive/mnms/issues?q=label%3Atesting+) and other regression tests based on each chapter of the user guide manual. 
- distribute test work to everyone. 

- Once tests are done, document  and review the results.

After all tests, the release can proceed as documented.

## How to make the release package 

The release package `bbnim_linux_amd64_v1.0.X.zip`, `bbnim_windows_amd64_v1.0.X.zip`, `bbnim-v0.0.99-linux-installer.run` and `bbnim-v0.0.99-windows-installer.exe` can be built with `make release` in **testbed**.

- bbnim_linux_amd64_v1.0.X.zip: Contains all Linux binaries (services), PDF user manual, and necessary shared libraries install script for Linux.
- bbnim_windows_amd64_v1.0.X.zip: Contains all Windows executables, PDF user manual, and required DLLs for Windows.
- bbnim-v0.0.99-linux-installer.run: Self-extracting Linux installer script that provides a step-by-step installation guide (select install directory, verify dependencies, install binaries and docs).
- bbnim-v0.0.99-windows-installer.exe: Windows setup wizard that guides users through installation steps (choose destination folder, accept license, create shortcuts, finish setup).

The Makefile contains most of the steps here including building binaries, bundling documents, and fetching third-party tools.  
Please refer to `make help` for more details.

## Testing

- Merge all PRs and freeze 

- Run all unit tests before and after each PR

- Run all manual tests described in doc/test.md directory of mnms github

- All team members must participate in testing all features and fixes

### Update CHANGELOG.MD

- Create a section for the new release version.
- List all new features.
- List all bug fixes since the previous release.
- changelig must be thoroughly documented 

### Where to run make

- Run make release in a shell on a Linux x86-64 machine

- The cross compiling will produce binaries for Linux x86-64 and Windows x86-64

#### Make sure repository userguide is up to date

- Merge all PRs (userguide)
- review user guide
- revise as needed 
- produce pdf output from md
- updated user guide must be included in zip

#### Update MANIFEST

There should be a MANIFEST.md  file
in the NMS release ZIP file.

It is coped from MANIFESTForLinux.md 
or MANIFESTForWindows.md file 
for each platform specific zip file. 

### Make sure frontend will build OK

- cd into frontend directory 

- modify package.json to update version to 1.0.X or other appropriate value

- versions are based on semver

- 1.0.X where X is a number for example

- and run make

- this will npm install and build frontend GO binary that contains all nodejs stuff into a binary file

### Install system packages

```
sudo make install_system_packages
```

### Make release

```shell
make release
```

- Enter version (ex: v1.0.0): v1.0.X

- v1.0.X where X is a number for example

- This will create mnms service binaries with correct version info and place binaries for supported target architectures in release dir

- then the files in release directory will be archived into a ZIP file called `bbnim_linux_amd64_v1.0.X.zip` and `bbnim_windows_amd64_v1.0.X.zip`

- it will create two files bbnim_v1.0.X-linux-installer.run and bbnim_v1.0.X-windows-installer.exe for user

- do unzip -t `bbnim_linux_amd64_v1.0.X.zip` and `bbnim_windows_amd64_v1.0.X.zip` to see it contains correct content

- ask people to test installation and functionalities of the content of `bbnim_linux_amd64_v1.0.X.zip` and `bbnim_windows_amd64_v1.0.X.zip`

- people can use bbnim_v1.0.X-linux-installer.run or bbnim_v1.0.X-windows-installer.exe to install

  the contents is same as zip file

### Code signing for Windows

Since Mircosoft may false report binaries or libraries not safe. We may ask Philip for help to sign the binaries and libraries for Windows and then re-pack.


### Archive the release

- The zip file can be distributed as a release but before distributing please make sure to keep it in the archive.

- Go to https://github.com/bbtechhive/mnms/releases  and click "draft a new release"

- Click "choose a tag" and create a new tag like v1.0.X

- Attach the release zip files

- After creating the release you should have a new tag you created in https://github.com/bbtechhive/mnms/tags

- Upload zip files or installer to https://nimbl.blackbeartechhive.com/files

## Publish the release

If there is a web site to publish the release please upload the content to the website in appropriately secure manner.

For now, the release is sent out to a select number of people via email -- to Shawn, Matteo, Civic, Pochih, Philip, Eric, et. al.



