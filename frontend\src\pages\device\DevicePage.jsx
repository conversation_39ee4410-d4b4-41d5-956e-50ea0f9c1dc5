import React, { useState } from "react";
import { useThemeStore } from "../../utils/themes/useStore";
import {
  useGetInventriesQuery,
  useSendCommandMutation,
} from "../../app/services/commandApi";
import { useDispatch, useSelector } from "react-redux";
import {
  inventorySliceSelector,
  setSelectedRowKeys,
} from "../../features/inventory/inventorySlice";
import { ProTable } from "@ant-design/pro-components";
import { deviceColumns } from "../../components/table-columns/device-table";
import {
  App,
  Button,
  List,
  Segmented,
  Space,
  Table,
  Tooltip,
  Typography,
  theme as antdTheme,
} from "antd";
import DeviceContextMenu from "../../components/context-menu/device-menu";
import { useContextMenu } from "react-contexify";
import { mapDeviceDataForExportOnOff } from "../../utils/comman/dataMapping";
import ExportData from "../../components/exportData/ExportData";
import Icon, {
  CloseCircleFilled,
  CloseOutlined,
  ExclamationCircleFilled,
  PlusOutlined,
} from "@ant-design/icons";
import { RequestLocateDevice } from "../../features/singleDeviceConfigurations/locateDeviceSlice";
import { RequestRebootDevice } from "../../features/singleDeviceConfigurations/rebootDeviceSlice";
import { RequestEnableSNMP } from "../../features/singleDeviceConfigurations/enableSNMPDeciceSlice";
import FwUpdateModel from "../../components/device/FwUpdateModel";
import TrapSettingModel from "../../components/device/TrapSettingModel";
import SyslogSettimgModel from "../../components/device/SyslogSettimgModel";
import PortInfoModel from "../../components/device/PortInfoModel";
import { RequestSaveRunningConfig } from "../../features/singleDeviceConfigurations/saveRunningConfigSlice";
import { openNetworkSettingDrawer } from "../../features/singleDeviceConfigurations/singleNetworkSetting";
import { openSyslogSettingDrawer } from "../../features/singleDeviceConfigurations/singleSyslogSetting";
import { openTrapSettingDrawer } from "../../features/singleDeviceConfigurations/singleTrapSetting";
import { openFwUpdateDrawer } from "../../features/singleDeviceConfigurations/updateFirmwareDeviceSlice";
import NetworkSettingModel from "../../components/mdr/NetworkSettingModel";
import MdrSetLedModel from "../../components/mdr/MdrSetLedModel";
import MdrProfinetModel from "../../components/mdr/MdrProfinetModel";
import MdrConfigModel from "../../components/mdr/MdrConfigModel";
import ScanByIPRangeModel from "../../components/device/ScanByIPRangeModel";
import DeviceEditModel from "../../components/device/DeviceEditModel";
import AiButton from "../../components/aiassistant/aibutton";
import { showChatSession } from "../../features/aiassist/aiassistSlice";
import { ChatSession } from "../../components/aiassistant/ChatSession";

const { Title, Text } = Typography;

const DevicePage = () => {
  const { token } = antdTheme.useToken();
  const { modal, notification } = App.useApp();
  const { inventoryType, changeInventoryType } = useThemeStore();
  const { show, hideAll } = useContextMenu();
  const [contextRecord, setContextRecord] = useState({});
  const [inputSearch, setInputSearch] = useState("");
  const [fwModelOpen, setFwModelOpen] = useState(false);
  const [trapModelOpen, setTrapModelOpen] = useState(false);
  const [syslogModelOpen, setSyslogModelOpen] = useState(false);
  const [openPortInfo, setOpenPortInfo] = useState(false);
  //open model state
  const [openNetSetModel, setOpenNetSetModel] = useState(false);
  const [openLedSetModel, setOpenLedSetModel] = useState(false);
  const [openProfSetModel, setOpenProfSetModel] = useState(false);
  const [openMdrSetModel, setOpenMdrSetModel] = useState(false);
  const [openScanbyIP, setOpenScanbyIP] = useState(false);
  const dispatch = useDispatch();
  const { selectedRowKeys } = useSelector(inventorySliceSelector);
  const {
    data = [],
    isFetching,
    refetch,
  } = useGetInventriesQuery(inventoryType, { refetchOnMountOrArgChange: true });
  const [sendCommand, {}] = useSendCommandMutation();
  const [sendFwCommand, { isLoading: massUpdateLoading }] =
    useSendCommandMutation();

  const [sendCommandConfig, { isLoading: configSetLoading }] =
    useSendCommandMutation();

  const handleDeleteAllDevice = async () => {
    try {
      const command = [
        {
          command: "device delete all",
          all: true,
        },
      ];
      await sendCommand(command).unwrap();
      notification.success({
        message: "successfully command sent!",
      });
      refetch();
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    }
  };

  const handleContextMenuClick = (key, data, selectedKey) => {
    console.log(key, data, selectedKey);
    const {
      ipaddress,
      mac,
      netmask,
      gateway,
      hostname,
      isdhcp,
      modelname,
      tunneled_url,
      syslogSetting,
      trapSetting,
    } = data;

    switch (key) {
      case "openweb":
        window.open(`http://${ipaddress}`, "_blank");
        break;
      case "openwebtunnel":
        if (tunneled_url !== "") {
          window.open(`${tunneled_url}`, "_blank");
        } else handleAddSSHTunnel(mac);
        break;
      case "beep":
        modal.confirm({
          ...confirmConfig,
          content: (
            <ConfirmContent
              icon={() => <ExclamationCircleFilled />}
              title="Confirm Beep Device"
              text="This will let device beep."
            />
          ),
          onOk() {
            dispatch(RequestLocateDevice({ mac, ipaddress }));
          },
          onCancel() {
            console.log("Cancel");
          },
        });
        break;
      case "reboot":
        modal.confirm({
          ...confirmConfig,
          content: (
            <ConfirmContent
              icon={() => <CloseCircleFilled />}
              title="Confirm Reboot Device"
              text="This will let device reboot."
            />
          ),
          onOk() {
            dispatch(RequestRebootDevice({ mac, ipaddress }));
          },
          onCancel() {
            console.log("Cancel");
          },
        });
        break;
      case "enablesnmp":
        modal.confirm({
          ...confirmConfig,
          content: (
            <ConfirmContent
              icon={() => <ExclamationCircleFilled />}
              title="Device SNMP enable"
              text="This will enable device SNMP."
            />
          ),
          onOk() {
            dispatch(RequestEnableSNMP({ mac, ipaddress }));
          },
          onCancel() {
            console.log("Cancel");
          },
        });
        break;
      case "networkSetting":
        dispatch(
          openNetworkSettingDrawer({
            ipaddress,
            mac,
            netmask,
            gateway,
            hostname,
            new_ip_address: ipaddress,
            modelname,
            isdhcp,
          })
        );
        break;
      case "syslogSetting":
        dispatch(
          openSyslogSettingDrawer({
            mac,
            modelname,
            syslogSetting,
          })
        );
        break;

      case "trapSetting":
        dispatch(
          openTrapSettingDrawer({
            mac,
            modelname,
            trapSetting,
          })
        );
        break;
      case "uploadFirmware":
        dispatch(
          openFwUpdateDrawer({
            mac,
            modelname,
          })
        );
        break;
      case "saveConfig":
        modal.confirm({
          ...confirmConfig,
          content: (
            <ConfirmContent
              icon={() => <ExclamationCircleFilled />}
              title="Device save config"
              text="This will save device."
            />
          ),
          onOk() {
            dispatch(RequestSaveRunningConfig({ mac_address: mac }));
          },
          onCancel() {
            console.log("Cancel");
          },
        });
        break;
      case "massReboot":
        handleDevicesReboot(selectedKey);
        break;
      case "massEnablesnmp":
        handleDevicesEnableSnmp(selectedKey);
        break;
      case "deleteDevice":
        handleDevicesDelete(selectedKey);
        break;
      case "massUploadFirmware":
        setFwModelOpen(true);
        break;
      case "massTrapSetting":
        setTrapModelOpen(true);
        break;
      case "massSyslogSetting":
        setSyslogModelOpen(true);
        break;
      case "portInfo":
        setContextRecord(data);
        setOpenPortInfo(true);
        break;
      case "setNetwork":
        setContextRecord(data);
        setOpenNetSetModel(true);
        break;
      case "setLed":
        setContextRecord(data);
        setOpenLedSetModel(true);
        break;
      case "setMdr":
        setContextRecord(data);
        setOpenMdrSetModel(true);
        break;
      case "setProfinet":
        setContextRecord(data);
        setOpenProfSetModel(true);
        break;
      default:
        break;
    }
  };

  const confirmConfig = { icon: null, className: "confirm-class", width: 360 };
  const ConfirmContent = ({ icon, title, text }) => (
    <Space align="center" direction="vertical" style={{ width: "100%" }}>
      <Icon
        component={icon}
        style={{
          color: token.colorWarning,
          fontSize: 64,
        }}
      />
      <Title level={4}>{title}</Title>
      <Text strong>{text}</Text>
    </Space>
  );

  const handleAddSSHTunnel = async (mac) => {
    try {
      const command = [
        {
          command: `agent ssh reverse websrv ${mac}`,
        },
      ];
      await sendCommand(command).unwrap();
      notification.success({
        message: "no tunnel url, sent command to add tunnel!",
      });
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    }
  };

  const handleDevicesReboot = async (selectedKeys) => {
    if (selectedKeys.length <= 0) {
      notification.error({ message: "please select device!" });
      return;
    }

    try {
      const confirmed = await modal.confirm({
        title: "Devices Reboot",
        content: "Do you want to reboot for selected devices ?",
      });
      if (!confirmed) {
        return;
      }
      const command = selectedKeys.map((mac) => ({
        command: `reset ${mac}`,
      }));
      await sendCommand(command).unwrap();
      notification.success({
        message: "successfully command sent!",
      });
      refetch();
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    } finally {
      dispatch(setSelectedRowKeys([]));
    }
  };

  const handleDevicesEnableSnmp = async (selectedKeys) => {
    if (selectedKeys.length <= 0) {
      notification.error({ message: "please select device!" });
      return;
    }

    try {
      const confirmed = await modal.confirm({
        title: "Enable SNMP",
        content: "Do you want to enable SNMP for selected devices ?",
      });
      if (!confirmed) {
        return;
      }
      const command = selectedKeys.map((mac) => ({
        command: `snmp enable ${mac}`,
      }));
      await sendCommand(command).unwrap();
      notification.success({
        message: "successfully command sent!",
      });
      refetch();
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    } finally {
      dispatch(setSelectedRowKeys([]));
    }
  };

  const handleDevicesDelete = async (selectedKeys) => {
    if (selectedKeys.length <= 0) {
      notification.error({ message: "please select device!" });
      return;
    }

    try {
      const confirmed = await modal.confirm({
        title: "Delete Device",
        content: "Do you want to delete selected devices ?",
      });
      if (!confirmed) {
        return;
      }
      const command = selectedKeys.map((mac) => ({
        command: `device delete ${mac}`,
      }));
      await sendCommand(command).unwrap();
      notification.success({
        message: "successfully command sent!",
      });
      refetch();
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    } finally {
      dispatch(setSelectedRowKeys([]));
    }
  };

  const handleDevicesFwUpgrade = async (value) => {
    if (selectedRowKeys.length <= 0) {
      notification.error({ message: "please select device!" });
      return;
    }
    try {
      const command = selectedRowKeys.map((mac) => ({
        command: `firmware update ${mac} ${value.fwUrl}`,
      }));
      await sendFwCommand(command).unwrap();
      notification.success({
        message: "successfully command sent!",
      });
      refetch();
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    } finally {
      dispatch(setSelectedRowKeys([]));
      setFwModelOpen(false);
    }
  };

  const handleDevicesTrapSetting = async (value) => {
    if (selectedRowKeys.length <= 0) {
      notification.error({ message: "please select device!" });
      return;
    }
    try {
      const command = selectedRowKeys.map((mac) => ({
        command: `snmp trap add ${mac} ${value.serverIp} ${value.serverPort} ${value.comString}`,
      }));
      await sendFwCommand(command).unwrap();
      notification.success({
        message: "successfully command sent!",
      });
      refetch();
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    } finally {
      dispatch(setSelectedRowKeys([]));
      setTrapModelOpen(false);
    }
  };

  const handleDevicesSyslogSetting = async (value) => {
    if (selectedRowKeys.length <= 0) {
      notification.error({ message: "please select device!" });
      return;
    }
    try {
      const command = selectedRowKeys.map((mac) => ({
        command: `config syslog set ${mac} ${value.logToServer ? 1 : 0} ${
          value.serverIP
        } ${value.serverPort} ${value.logLevel} ${value.logToFlash ? 1 : 0}`,
      }));
      await sendFwCommand(command).unwrap();
      notification.success({
        message: "successfully command sent!",
      });
      refetch();
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    } finally {
      dispatch(setSelectedRowKeys([]));
      setSyslogModelOpen(false);
    }
  };

  const handleSetMdrNetwork = async (values) => {
    const newIp = values.isdhcp ? "0.0.0.0" : values.newipaddress;
    const dhcp = values.isdhcp ? 1 : 0;
    try {
      const command = [
        {
          command: `config network set ${values.mac} ${values.ipaddress} ${newIp} ${values.netmask} ${values.gateway} ${values.hostname} ${dhcp}`,
        },
      ];
      await sendCommandConfig(command).unwrap();
      notification.success({
        message: "successfully command sent!",
      });
      handleRefresh();
    } catch (error) {
      notification.error({
        message:
          error.data.error || error.data || error || "somthing went wrong",
      });
    } finally {
      setOpenNetSetModel(false);
      setContextRecord({});
    }
  };

  const handleSetMdrLed = async (values) => {
    try {
      const command = [
        {
          command: `agent motor led set ${values.mac} ${values.led}`,
        },
      ];
      await sendCommandConfig(command).unwrap();
      notification.success({
        message: "successfully command sent!",
      });
      handleRefresh();
    } catch (error) {
      notification.error({
        message:
          error.data.error || error.data || error || "somthing went wrong",
      });
    } finally {
      setOpenLedSetModel(false);
      setContextRecord({});
    }
  };

  const handleSetMdrProfinet = async (values) => {
    try {
      const command = [
        {
          command: `agent motor profinet set ${values.mac} ${values.profinet}`,
        },
      ];
      await sendCommandConfig(command).unwrap();
      notification.success({
        message: "successfully command sent!",
      });
      handleRefresh();
    } catch (error) {
      notification.error({
        message:
          error.data.error || error.data || error || "somthing went wrong",
      });
    } finally {
      setOpenProfSetModel(false);
      setContextRecord({});
    }
  };

  const handleSetMdrConfig = async (values) => {
    const { mac, zone, mode, holding, speed, direction, level, sensor } =
      values;
    try {
      const command = [
        {
          command: `agent motor mdr set ${mac} ${zone} ${mode} ${holding} ${speed} ${direction} ${level} ${sensor}`,
        },
      ];
      await sendCommandConfig(command).unwrap();
      notification.success({
        message: "successfully command sent!",
      });
      handleRefresh();
    } catch (error) {
      notification.error({
        message:
          error.data.error || error.data || error || "somthing went wrong",
      });
    } finally {
      setOpenMdrSetModel(false);
      setContextRecord({});
    }
  };

  const handleScanIPrange = async (value) => {
    try {
      const command = [
        {
          command: `scan ${value.cidr} -save`,
        },
      ];
      await sendCommand(command).unwrap();
      notification.success({
        message: "scan by IP Range (CIDR) command sent !",
      });
    } catch (error) {
      notification.error({ message: error.data.error || error.data || error });
    } finally {
      setOpenScanbyIP(false);
      refetch();
    }
  };

  const recordAfterfiltering = (dataSource) => {
    return dataSource.filter((row) => {
      let rec = deviceColumns().map((element) => {
        return row[element.dataIndex]
          ?.toString()
          ?.toLowerCase()
          ?.includes(inputSearch?.toLowerCase());
      });
      return rec.includes(true);
    });
  };

  return (
    <>
      <div data-testid="device-table-div">
        <ProTable
          columns={deviceColumns(token)}
          dataSource={recordAfterfiltering(data) || []}
          rowKey="mac"
          defaultSize="small"
          loading={isFetching}
          options={{
            reload: () => {
              refetch();
            },
            fullScreen: true,
          }}
          search={false}
          toolbar={{
            search: {
              allowClear: true,
              onSearch: (value) => setInputSearch(value),
              onClear: () => setInputSearch(""),
            },
            actions: [
              <AiButton
                key="ai-button"
                script="device"
                onClick={() => dispatch(showChatSession())}
              />,
              <Segmented
                options={["device", "mdr"]}
                value={inventoryType}
                onChange={(v) => {
                  changeInventoryType(v);
                  dispatch(setSelectedRowKeys([]));
                }}
              />,
              <ExportData
                Columns={deviceColumns(token)}
                DataSource={mapDeviceDataForExportOnOff(data)}
                title="Inventory_Device_List"
              />,
              <>
                {selectedRowKeys.length <= 0 ? (
                  <Tooltip title="clear all devices">
                    <Button
                      icon={<CloseOutlined />}
                      onClick={handleDeleteAllDevice}
                    />
                  </Tooltip>
                ) : null}
              </>,
              <Tooltip title="add device by ip range">
                <Button
                data-testid="scan-ip-btn"
                  icon={<PlusOutlined />}
                  onClick={() => setOpenScanbyIP(true)}
                />
              </Tooltip>,
            ],
          }}
          pagination={{
            position: ["bottomCenter"],
            showQuickJumper: true,
            size: "default",
            total: (recordAfterfiltering(data) || []).length,
            defaultPageSize: 10,
            pageSizeOptions: [10, 15, 20, 25],
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} items`,
          }}
          expandable={{
            expandedRowRender: (record) => (
              <List
                size="small"
                header={<div>Device Errors</div>}
                bordered
                dataSource={record.device_errors?.slice(0, 5) || []}
                renderItem={(item) => <List.Item>{item}</List.Item>}
              />
            ),
            rowExpandable: (record) => record.device_errors !== null,
          }}
          scroll={{
            x: 1100,
          }}
          rowSelection={
            inventoryType === "mdr"
              ? undefined
              : {
                  selectedRowKeys,
                  onChange: (newSelectedRowKeys) => {
                    hideAll();
                    dispatch(setSelectedRowKeys(newSelectedRowKeys));
                  },
                  selections: [Table.SELECTION_ALL, Table.SELECTION_NONE],
                }
          }
          cardProps={{
            style: { boxShadow: token?.Card?.boxShadow },
          }}
          dateFormatter="string"
          columnsState={{
            persistenceKey: "nms-device-table",
            persistenceType: "localStorage",
          }}
          onRow={(record, rowIndex) => {
            return {
              onContextMenu: async (event) => {
                if (record) {
                  show({
                    id:
                      inventoryType === "device"
                        ? selectedRowKeys.length === 0
                          ? "device-menu"
                          : "mass-menu"
                        : "mdr-menu",
                    event,
                    props: {
                      record,
                      selectedRowKeys,
                    },
                  });
                }
              },
            };
          }}
        />
        <DeviceContextMenu onMenuItemClicked={handleContextMenuClick} />
        <ScanByIPRangeModel
          open={openScanbyIP}
          onOk={handleScanIPrange}
          onCancel={() => setOpenScanbyIP(false)}
        />
        <DeviceEditModel />
        <FwUpdateModel
          open={fwModelOpen}
          onCancel={() => {
            setFwModelOpen(false);
            dispatch(setSelectedRowKeys([]));
          }}
          onOk={handleDevicesFwUpgrade}
          loading={massUpdateLoading}
        />
        <TrapSettingModel
          open={trapModelOpen}
          onCancel={() => {
            setTrapModelOpen(false);
            dispatch(setSelectedRowKeys([]));
          }}
          onOk={handleDevicesTrapSetting}
          loading={massUpdateLoading}
        />
        <SyslogSettimgModel
          open={syslogModelOpen}
          onCancel={() => {
            setSyslogModelOpen(false);
            dispatch(setSelectedRowKeys([]));
          }}
          onOk={handleDevicesSyslogSetting}
          loading={massUpdateLoading}
        />
        <PortInfoModel
          open={openPortInfo}
          onCancel={() => {
            setOpenPortInfo(false);
            setContextRecord({});
          }}
          recordData={contextRecord}
        />
        <NetworkSettingModel
          record={contextRecord}
          open={openNetSetModel}
          onCancel={() => {
            setOpenNetSetModel(false);
            setContextRecord({});
          }}
          onOk={handleSetMdrNetwork}
          loading={configSetLoading}
        />
        <MdrSetLedModel
          record={contextRecord}
          open={openLedSetModel}
          onCancel={() => {
            setOpenLedSetModel(false);
            setContextRecord({});
          }}
          onOk={handleSetMdrLed}
          loading={configSetLoading}
        />
        <MdrProfinetModel
          record={contextRecord}
          open={openProfSetModel}
          onCancel={() => {
            setOpenProfSetModel(false);
            setContextRecord({});
          }}
          onOk={handleSetMdrProfinet}
          loading={configSetLoading}
        />
        <MdrConfigModel
          record={contextRecord}
          open={openMdrSetModel}
          onCancel={() => {
            setOpenMdrSetModel(false);
            setContextRecord({});
          }}
          onOk={handleSetMdrConfig}
          loading={configSetLoading}
        />
      </div>
    </>
  );
};

export default DevicePage;
