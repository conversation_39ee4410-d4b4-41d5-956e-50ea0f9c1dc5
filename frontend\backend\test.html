<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Stream Response Example</title>
</head>

<body>
  <h1>Stream Response Data</h1>
  <div id="createServices"></div>

  <button onclick="fetchCreateServices()">Create licenses</button>

  <div>
    <h2>Services</h2>
    <select id="servicesList"></select>
    <button onclick="fetchRunServices()">Run selected Services</button>
    <button onclick="fetchRunAll()">Run all</button>
    <div id="runServices"></div>
    <div id="streamData">
      <h3>Output: </h3>
    </div>
  </div>



  <script>


    // function to POST http://localhost:9000/api/v1/services/from_license
    async function fetchCreateServices() {
      const url = 'http://localhost:9000/api/v1/services/from_license'; // Adjusted to the POST endpoint
      const createServicesElement = document.getElementById('createServices');
      try {
        const response = await fetch(url, {
          method: 'POST', // Specify the method as POST
          headers: {
            'Content-Type': 'application/json', // Assuming JSON content. Adjust if necessary.
            // Include additional headers here if required
          },
          body: JSON.stringify({
            privKeyPath:""
          }),
        });
        const data = await response.json();
        if (response.status !== 200) {
          createServicesElement.innerHTML += `<p>Failed to create services: ${JSON.stringify(data)} </p>`;
          
        }else{
          createServicesElement.innerHTML += `<p>Services created: ${JSON.stringify(data)}</p>`;
        }

       
      } catch (error) {
        console.error('Fetch error:', error);
        createServicesElement.innerHTML += `<p>Error fetching data: ${error.message}</p>`;
      }

      // fetch service
      const url2 = 'http://localhost:9000/api/v1/services';
      const servicesListElement = document.getElementById('servicesList');
      try {
        const response = await fetch(url2);
        console.log("fetch services",response);
        const data = await response.json();
        if (response.status !== 200) {
          servicesListElement.innerHTML += `<p>Failed to fetch services: ${JSON.stringify(data)} </p>`;
          return;
        }
        servicesListElement.innerHTML = '';
        data.services? data.services.forEach((service) => {
          servicesListElement.innerHTML += `<option>${service.id}</option>`;
        }):null;
      } catch (error) {
        console.error('Fetch error:', error);
        servicesListElement.innerHTML += `<p>Error fetching data: ${error.message}</p>`;
      }

        
    }


    async function fetchRunServices() {
      const serviceID = document.getElementById('servicesList').value;
      const url = `http://localhost:9000/api/v1/services/run`;
      const runServicesElement = document.getElementById('runServices');
      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name:serviceID,
          }),
        });
        const data = await response.json();
        if (response.status !== 200) {
          runServicesElement.innerHTML += `<p>Failed to run services: ${JSON.stringify(data)} </p>`;
          return;
        }
        runServicesElement.innerHTML += `<p>Services running: ${JSON.stringify(data)}</p>`;
      } catch (error) {
        console.error('Fetch error:', error);
        runServicesElement.innerHTML += `<p>Error fetching data: ${error.message}</p>`;
      }

    }

    window.onload = fetchStreamData;
    

    // listen http://localhost:9000/api/v1/services/stream when in to this page
    async function fetchStreamData() {
      const url = 'http://localhost:9000/api/v1/services/run/socket';
      const runServicesElement = document.getElementById('streamData');
      console.log("fetchStreamData");
      const ws = new WebSocket(url);
      ws.onopen = function () {
        console.log('WebSocket Client Connected');
      };
      ws.onmessage = function (e) {
        console.log("Received: '" + e.data + "'");
        runServicesElement.innerHTML += `<p>${e.data}</p>`;
      };
      ws.onclose = function () {
        console.log('WebSocket Client Closed');
      };
      ws.onerror = function (e) {
        console.log('WebSocket Client Error', e);
      };
    }
    
    async function fetchRunAll() {
      const url = `http://localhost:9000/api/v1/services/run/all`;
      const runServicesElement = document.getElementById('runServices');
      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({}),
        });
        const data = await response.json();
        if (response.status !== 200) {
          runServicesElement.innerHTML += `<p>Failed to run services: ${JSON.stringify(data)} </p>`;
          return;
        }
        runServicesElement.innerHTML += `<p>Services running: ${JSON.stringify(data)}</p>`;
      } catch (error) {
        console.error('Fetch error:', error);
        runServicesElement.innerHTML += `<p>Error fetching data: ${error.message}</p>`;
      }
    }

  </script>
</body>

</html>

