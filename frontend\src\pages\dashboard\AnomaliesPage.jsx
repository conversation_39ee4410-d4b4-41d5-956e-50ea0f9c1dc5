import React, { useState } from "react";
import dayjs from "dayjs";
import { useGetAnomalyStatisticsQuery } from "../../app/services/anomalyApi";
import {
  But<PERSON>,
  Card,
  Col,
  Collapse,
  Pagination,
  Result,
  Row,
  Space,
  Spin,
  Typography,
  theme,
} from "antd";
import { InboxOutlined, ReloadOutlined } from "@ant-design/icons";
import AnomalySettingStats from "../../components/dashboard/AnomalySettingStats";
import EventListCard from "../../components/dashboard/EventListCard";

const AnomaliesPage = () => {
  const {
    data: statsData = [],
    isLoading,
    refetch,
  } = useGetAnomalyStatisticsQuery({}, { refetchOnMountOrArgChange: true });

  const token = theme.useToken().token;
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(5);

  const panelStyle = {
    marginBottom: 8,
    background: token.colorBgContainer,
    borderRadius: token.borderRadiusSM,

    boxShadow: token?.Card?.boxShadow,
    border: "none",
    alignItem: "center",
  };

  const onPageChange = (page, pageSize) => {
    setCurrent(page);
    setPageSize(pageSize);
  };

  if (isLoading) {
    return (
      <div style={{ textAlign: "center" }}>
        <Spin size="large" />
      </div>
    );
  }
  const items = statsData
    ?.slice((current - 1) * pageSize, (current - 1) * pageSize + pageSize)
    .map((item) => ({
      key: item?.clientname,
      label: (
        <Space size="large">
          <Typography.Title type="secondary" level={5} style={{ margin: 0 }}>
            Service name: {item?.clientname}
          </Typography.Title>
          <Typography.Title type="secondary" level={5} style={{ margin: 0 }}>
            start at: {dayjs(item?.since).format("YYYY/MM/DD HH:mm:ss")}
          </Typography.Title>
        </Space>
      ),
      children: (
        <AnomalySettingStats
          clientName={item?.clientname}
          settings={item?.settings}
        />
      ),
      style: panelStyle,
    }));

  return (
    <Row gutter={[16, 16]}>
      <Col xs={24} md={24} lg={18}>
        <Card
          title="Anomaly Stats and Report"
          bodyStyle={{ padding: "5px" }}
          extra={<Button icon={<ReloadOutlined />} onClick={() => refetch()} />}
          actions={[
            <Pagination
              showSizeChanger
              current={current}
              defaultCurrent={1}
              onChange={onPageChange}
              pageSize={pageSize}
              pageSizeOptions={[5, 10]}
              total={statsData?.length}
            />,
          ]}
        >
          {statsData?.length > 0 ? (
            <Collapse
              accordion
              items={items}
              force
              className="custom-colapse"
            />
          ) : (
            <Result
              icon={<InboxOutlined />}
              title="No anomaly services found, Please run anomaly services"
            />
          )}
        </Card>
      </Col>
      <Col xs={24} md={24} lg={6}>
        <EventListCard />
      </Col>
    </Row>
  );
};

export default AnomaliesPage;
