import React, { useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import {
  <PERSON><PERSON>,
  Card,
  Modal,
  Space,
  Typography,
  Spin,
  Flex,
  Tooltip,
  Row,
  Col,
  Alert,
} from "antd";
import { <PERSON><PERSON>ble, Sender } from "@ant-design/x";
import markdownit from "markdown-it";
import hljs from "highlight.js";
import {
  ReloadOutlined,
  UserOutlined,
  RobotOutlined,
  SendOutlined,
} from "@ant-design/icons";
import { useStreamingFetchV2 } from "../../features/aiassist/useStreamingFetch";
import { useSelector, useDispatch } from "react-redux";
import {
  selectAssistantFlow,
  selectShowChatSession,
  hideChatSession,
} from "../../features/aiassist/aiassistSlice";
import ConfirmToolCall from "./ConfirmToolCall";
import { useStreamingStore } from "../../features/aiassist/streamingStore";

const { Title, Text: AntText, Paragraph } = Typography;

function CollapsibleProcessing({ messages, isLoading, onClear, md }) {
  const [open, setOpen] = useState(true);
  return (
    <Card
      variant="bordered"
      size="small"
      title="Processing messages"
      styles={{ body: { maxHeight: "70vh" } }}
    >
      {open && (
        <div style={{ overflowY: "auto", height: "65vh" }}>
          {messages.map(
            (msg, idx) =>
              msg && (
                <Paragraph
                  key={idx}
                  type="secondary"
                  style={{ marginBottom: 8 }}
                >
                  {md(String(msg))}
                </Paragraph>
              )
          )}
        </div>
      )}
    </Card>
  );
}

CollapsibleProcessing.propTypes = {
  messages: PropTypes.arrayOf(PropTypes.string),
  isLoading: PropTypes.bool,
  onClear: PropTypes.func.isRequired,
};

export function ChatSession({ nodeid }) {
  // useState hooks first
  const [prompt, setPrompt] = useState("");
  const flow = useSelector(selectAssistantFlow);
  const showChatSession = useSelector(selectShowChatSession);
  const dispatch = useDispatch();
  // Custom hooks
  const {
    sessionID,
    processing,
    error,
    isLoading,
    toolCalls,
    startStreaming,
    resetSession,
    resetToolCalls,
  } = useStreamingFetchV2();

  const { chatHistory, addToChatHistory } = useStreamingStore();

  console.log("sessionID", sessionID);

  // Event handlers
  const handleChat = async () => {
    if (!prompt.trim()) return;

    // Add user message to chat history
    addToChatHistory({ type: "user", content: prompt });

    await startStreaming("/api/v1/ai-assistant/session/query", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        flow,
        nodeid,
        prompt,
        sessionid: sessionID,
      }),
    });

    // Clear input after sending
    setPrompt("");
  };

  const handleNewSession = () => {
    resetSession();
    resetToolCalls();
    setPrompt("");
  };

  // handleResume
  const handleResume = async () => {
    await startStreaming("/api/v1/ai-assistant/session/resume", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        sessionid: sessionID,
      }),
    });
  };

  // Add this to the ChatSession component
  const handleClearProcessing = () => {
    if (processing) {
      processing.length = 0;
      // Force a re-render
      startStreaming(null);
    }
  };

  const md = markdownit({
    html: true,
    breaks: true,
    highlight: (str, lang) => {
      if (lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlightAuto(str).value;
        } catch (__) {}
      }
      return ""; // use external default
    },
  });

  const renderMarkdown = (content) => (
    <Typography>
      {/* biome-ignore lint/security/noDangerouslySetInnerHtml: used in demo */}
      <div
        dangerouslySetInnerHTML={{
          __html: md.render(content),
        }}
      />
    </Typography>
  );

  const renderSend = (props = {}) => {
    const { ignoreLoading, placeholder, ...btnProps } = props;
    return (
      <Sender
        value={prompt}
        onChange={setPrompt}
        loading={isLoading}
        onSubmit={(msg) => handleChat(msg)}
        placeholder="ask somthing"
        onCancel={() => {
          setPrompt("");
        }}
        actions={(_, info) => {
          const { SendButton, SpeechButton } = info.components;
          if (isLoading) {
            return <Spin size="small" />;
          }
          return (
            <Flex gap={5} align="center">
              {/* <SpeechButton /> */}
              <Tooltip title={prompt ? "Send \u21B5" : "Please type something"}>
                <SendButton {...btnProps} />
              </Tooltip>
            </Flex>
          );
        }}
      />
    );
  };

  return (
    <Modal
      open={showChatSession}
      onCancel={() => {
        dispatch(hideChatSession());
      }}
      title={
        <Space direction="vertical" size="small">
          <Title level={4} style={{ margin: 0 }}>
            NIMBL Assistant
          </Title>
          <AntText type="secondary">Leverage NIMBL to assist you</AntText>
          {sessionID && <AntText code>{`Session: ${sessionID}`}</AntText>}
        </Space>
      }
      footer={[
        <Button key="new" icon={<ReloadOutlined />} onClick={handleNewSession}>
          New Session
        </Button>,
        <Button
          key="close"
          onClick={() => {
            dispatch(hideChatSession());
          }}
        >
          Close
        </Button>,
      ]}
      width="100%"
      style={{ top: 20 }}
      styles={{ body: { maxHeight: "70vh" } }}
      forceRender
      maskClosable={false}
      destroyOnClose
    >
      <Row gutter={[16, 16]}>
        <Col span={7}>
          <CollapsibleProcessing
            messages={processing}
            isLoading={isLoading}
            onClear={handleClearProcessing}
            md={renderMarkdown}
          />
        </Col>
        <Col span={17}>
          <Flex vertical gap={10} style={{ height: "70vh" }}>
            <div
              style={{
                flex: 1,
                overflowY: "auto",
                display: "flex",
                flexDirection: "column",
                gap: 15,
              }}
            >
              {chatHistory.map((m, index) => (
                <Bubble
                  key={index}
                  placement={m.type === "user" ? "end" : "start"}
                  header={m.type === "user" ? "You" : "NIMBL"}
                  content={m.content}
                  avatar={{
                    icon:
                      m.type === "user" ? <UserOutlined /> : <RobotOutlined />,
                  }}
                  messageRender={renderMarkdown}
                />
              ))}

              {/* Show tool call confirmations if there are tool calls */}
              {toolCalls && toolCalls.length > 0 && (
                <div style={{ marginBottom: 16 }}>
                  <ConfirmToolCall
                    sessionid={sessionID}
                    toolCalls={toolCalls}
                    onFinish={() => {
                      resetToolCalls();
                      console.log("finish tool call");
                      handleResume();
                    }}
                  />
                </div>
              )}

              {isLoading && (
                <Bubble
                  placement="start"
                  header="AI"
                  content="thinking..."
                  avatar={{ icon: <RobotOutlined /> }}
                  messageRender={renderMarkdown}
                />
              )}
            </div>
            {error && (
              <Alert
                message="Error"
                description={error.message || error}
                type="error"
                showIcon
              />
            )}
            {renderSend({
              variant: "text",
              placeholder: "Change button icon",
              color: "primary",
              icon: <SendOutlined />,
              shape: "default",
            })}
          </Flex>
        </Col>
      </Row>
    </Modal>
  );
}

export default ChatSession;
