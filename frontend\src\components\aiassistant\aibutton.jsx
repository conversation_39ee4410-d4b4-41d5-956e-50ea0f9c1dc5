import React from "react";
import PropTypes from "prop-types";
import { useDispatch } from "react-redux";
import { Button, Flex } from "antd";
import { SparklesIcon } from "@heroicons/react/24/outline";
import {
  showChatSession,
  setAssistantFlow,
} from "../../features/aiassist/aiassistSlice";

const AiButton = ({ value, onClick, ...restProps }) => {
  const dispatch = useDispatch();

  const handleClick = (e) => {
    let flow = value;
    if (flow == null || flow == "") {
      flow = "default";
    }
    dispatch(setAssistantFlow(flow));
    dispatch(showChatSession());

    // Call the external onClick handler if provided
    if (onClick) {
      onClick(e);
    }
  };

  return (
    <Flex align="center" gap="small">
      <Button
        icon={
          <SparklesIcon
            style={{
              width: "1.5rem",
              height: "1.5rem",
              color: "inherit", // inherits button text color
            }}
          />
        }
        onClick={handleClick}
        {...restProps}
      />
    </Flex>
  );
};

AiButton.propTypes = {
  value: PropTypes.string,
  onClick: PropTypes.func,
};

AiButton.defaultProps = {
  value: "default",
  onClick: undefined,
};

export default AiButton;
