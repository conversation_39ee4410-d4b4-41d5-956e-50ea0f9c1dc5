package utils

import (
	"archive/zip"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/shirou/gopsutil/process"
)

// GetNimblFileList send a request to https://nimbl.blackbeartechhive.com/api/v1/list
func GetNimblFileList() ([]string, error) {
	// GET https://nimbl.blackbeartechhive.com/api/v1/list
	type response struct {
		Files []struct {
			Name string `json:"name"`
			Type string `json:"type"`
		} `json:"files"`
	}

	ret := make([]string, 0)

	var res response
	// send a http request
	resp, err := http.Get("https://nimbl.blackbeartechhive.com/api/v1/list")
	if err != nil {
		return ret, err
	}
	defer resp.Body.Close()
	// read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return ret, err
	}

	// parse response
	err = json.Unmarshal(body, &res)
	if err != nil {
		return ret, err
	}

	// Search all file start with bbnim and type is zip
	for _, file := range res.Files {
		if strings.HasPrefix(file.Name, "bbnim") && file.Type == ".zip" {
			ret = append(ret, file.Name)
		}
	}

	return ret, nil
}

// WriteProgress
type WriteProgress struct {
	// Implement the io.Writer interface
	Total int64 `json:"total"` // Total number of bytes to write
	Count int64 `json:"count"` // Number of bytes written
}

// Write
func (wp *WriteProgress) Write(p []byte) (n int, err error) {
	n = len(p)
	wp.Count += int64(n)
	return
}

// DownloadFile download a file from https://nimbl.blackbeartechhive.com/api/v1/files/{filename}
func DownloadFile(filename string, filepath string, progress *WriteProgress) error {
	if progress == nil {
		progress = &WriteProgress{}
	}

	// create a file
	out, err := os.Create(filepath)
	if err != nil {
		return err
	}
	defer out.Close()

	// Get the data
	resp, err := http.Get("https://nimbl.blackbeartechhive.com/api/v1/files/" + filename)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("bad sataus %s", resp.Status)
	}

	progress.Total = resp.ContentLength
	fmt.Printf("Downloading %s, %d bytes\n", filename, resp.ContentLength)

	// TeeReader reads from r and writes to w, until either EOF is reached on r or an error occurs.
	_, err = io.Copy(out, io.TeeReader(resp.Body, progress))
	if err != nil {
		return err
	}

	return nil
}

// GetNimblFilesPath
func GetNimblFilesPath() (string, error) {
	// check ./nimbl first if not exist, check subfolders of ./nimbl return first found
	loc := "./nimbl"
	err := CheckNimblFiles(loc)
	if err == nil {
		return loc, nil
	}
	// iterator subfolders
	files, err := os.ReadDir("./nimbl")
	if err != nil {
		return "", err
	}
	for _, file := range files {
		if file.IsDir() {
			loc = path.Join("./nimbl", file.Name())
			err = CheckNimblFiles(loc)
			if err == nil {
				return loc, nil
			}
		}
	}
	return loc, fmt.Errorf("Didn't find Nimbl binary in ./nimbl, please install it.")
}

// checkFiles  checks if the files exist
func CheckNimblFiles(loc string) error {
	// check ./nimbl/windows_amd64 folder exist
	_, err := os.Stat(loc)
	if err != nil || os.IsNotExist(err) {
		return fmt.Errorf("Didn't find Nimbl binary %s, please install it.", loc)
	}

	files := []string{
		"bbrootsvc",
		"bbanomsvc",
		"bbidpsvc",
		"bblogsvc",
		"bbnmssvc",
	}
	for _, file := range files {
		if runtime.GOOS == "windows" {
			file = file + ".exe"
		}
		// check file exist
		fullpath := path.Join(loc, file)
		// check fullpath exist
		_, err := os.Stat(fullpath)
		if err != nil || os.IsNotExist(err) {
			return fmt.Errorf("Didn't find %s, please install it.", fullpath)
		}
	}
	return nil
}

// KillProcesses kills porcesses in the list
func KillProcesses(processes []string) error {
	// List all processes
	allProcesses, err := process.Processes()
	if err != nil {
		return err
	}

	killedAny := false
	for _, p := range allProcesses {
		name, err := p.Name()
		if err != nil {
			continue // Skip processes where the name cannot be retrieved
		}

		for _, processName := range processes {
			if name == processName {
				// Attempt to kill the process
				if err := p.Kill(); err != nil {
					log.Printf("Failed to kill process %s (PID: %d): %s\n", name, p.Pid, err)
					return err
				} else {
					log.Printf("Killed process %s (PID: %d)\n", name, p.Pid)
					killedAny = true
					// p.Pid until process was terminated, timeout after 2 seconds
					ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
					defer cancel()

					for {
						select {
						case <-ctx.Done():
							log.Println("Timeout reached while waiting for the process to terminate.")
							break
						default:
							running, err := p.IsRunning()
							if err != nil || !running {
								break
							}
							time.Sleep(500 * time.Millisecond)
						}
					}

				}
			}
		}

	}
	if !killedAny {
		return fmt.Errorf("no process named '%s' found to kill", processes)
	}
	return nil
}

// KillProcess kills the process by name
func KillProcess(processName string) error {
	// List all processes
	processes, err := process.Processes()
	if err != nil {
		return err
	}

	var killedAny bool
	for _, p := range processes {
		name, err := p.Name()
		if err != nil {
			continue // Skip processes where the name cannot be retrieved
		}

		if name == processName {
			// Attempt to kill the process
			if err := p.Kill(); err != nil {
				log.Printf("Failed to kill process %s (PID: %d): %s\n", name, p.Pid, err)
				return err
			} else {
				log.Printf("Killed process %s (PID: %d)\n", name, p.Pid)
				killedAny = true
			}
		}
	}

	if !killedAny {
		log.Printf("No process named '%s' found to kill\n", processName)
	}

	return nil
}

// nimbl processes
var nimblProcess = []string{"bblogsvc", "bbrootsvc", "bbnmssvc", "bbidpsvc", "bbanomsvc", "bbnimbl"}

// RunningSvcs returns the running nimbl services
func RunningSvcs() ([]string, error) {
	allProcess, err := process.Processes()
	if err != nil {
		return nil, fmt.Errorf("failed to get all processes: %v", err)
	}
	var runningSvcs []string
	for _, p := range allProcess {
		name, err := p.Name()
		if err != nil {
			continue
		}

		for _, svc := range nimblProcess {
			if len(name) < len(svc) {
				continue
			}
			if name[:len(svc)] == svc {
				runningSvcs = append(runningSvcs, name)
			}
		}
	}

	return runningSvcs, nil
}

// KillAllBBServices kills all services
func KillAllBBServices() error {
	bbsvcs, err := RunningSvcs()
	if err != nil {
		return err
	}
	for _, svc := range bbsvcs {
		err := KillProcess(svc)
		if err != nil {
			return err
		}
	}
	return nil
}

var DefaultRootURL = "http://localhost:27182"

// SvcCmmand represents a command of Nimbl (bb*) service
// ID: unique name of the service
// Path: path to the executable binary
// Cmd: command to run (for example, "bbrootsvc"), do not include .exe
// Args: arguments to the command (for example, "-n", "root"),
type SvcCommand struct {
	ID   string   `json:"id" mapstructure:"id"`
	Path string   `json:"path" mapstructure:"path"`
	Cmd  string   `json:"cmd" mapstructure:"cmd"`
	Args []string `json:"args" mapstructure:"args"`
}

// NewSvcCommands creates a new SvcCommands
func NewSvcCommand(name, cmd, dir string, args []string) *SvcCommand {
	return &SvcCommand{
		ID:   name,
		Path: dir,
		Cmd:  cmd,
		Args: args,
	}
}

// Cmd returns shell command
func (s *SvcCommand) CmdStr() string {
	// If windows add .exe
	runCmd := s.Cmd
	if runtime.GOOS == "windows" {
		runCmd = runCmd + ".exe"
	}
	if len(s.Path) > 0 {
		runCmd = path.Join(s.Path, runCmd)
	}
	if len(s.Args) > 0 {
		runCmd += " " + strings.Join(s.Args, " ")
	}
	return runCmd
}

// SvcCommands represents commands of Nimbl (bb*) services
type SvcCommands struct {
	RootURL  string       `json:"root_url"`
	Commands []SvcCommand `json:"commands"`
	Output   bytes.Buffer `json:"-"`
	mu       sync.Mutex   `json:"-"`
	Notify   chan string  `json:"-"`
}

// ClearCommands clears the commands
func (s *SvcCommands) ClearCommands() {
	s.Commands = []SvcCommand{}
}

func (s *SvcCommands) Write(p []byte) (n int, err error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	n = len(p)
	s.Output.Write(p)
	if s.Notify != nil {
		select {
		case s.Notify <- string(p):
		default:
			log.Println("Notify channel is full")
		}
	}
	return
}

// Cmds returns the command and arguments
func (s *SvcCommands) Items() []SvcCommand {
	return s.Commands
}

// NewServices creates a new ShellCommands
func NewServices(rooturl string) *SvcCommands {
	return &SvcCommands{
		RootURL: rooturl,
		Output:  bytes.Buffer{},
		Notify:  make(chan string, 1024),
	}
}

// IsIDExist checks if the service ID exists
func (s *SvcCommands) IsIDExist(id string) bool {
	for _, svc := range s.Commands {
		if svc.ID == id {
			return true
		}
	}
	return false
}

// GetCmd returns the command of the service
func (s *SvcCommands) GetCmd(id string) (*SvcCommand, error) {
	for _, svc := range s.Commands {
		if svc.ID == id {
			return &svc, nil
		}
	}
	return nil, fmt.Errorf("service %s not found", id)
}

// AddService adds a service to the ShellCommands, return index of the service
func (s *SvcCommands) AddService(name string, executablePath string, cmd string, args []string) error {
	if len(name) == 0 {
		return fmt.Errorf("service name is empty")
	}
	if s.IsIDExist(name) {
		return fmt.Errorf("service %s already exists", name)
	}
	// Add -n and  -r, if args does not contain them
	if !strings.Contains(strings.Join(args, " "), "-n") {
		args = append(args, "-n", name)
	}
	if !strings.Contains(strings.Join(args, " "), "-r") {
		args = append(args, "-r", s.RootURL)
	}

	s.Commands = append(s.Commands, *NewSvcCommand(name, cmd, executablePath, args))
	return nil
}

// UpsertService updates or inserts a service to the ShellCommands
func (s *SvcCommands) UpsertService(name string, dir string, cmd string) error {
	if len(name) == 0 {
		return fmt.Errorf("service name is empty")
	}
	cmdstrings := strings.Split(cmd, " ")
	runcmd := cmdstrings[0]
	args := cmdstrings[1:]

	if s.IsIDExist(name) {
		return s.UpdateCommand(name, runcmd, dir, args)
	} else {
		return s.AddService(name, dir, runcmd, args)
	}
}

// UpsertShellCommand upsert the command of the service
func (s *SvcCommands) UpsertShellCommand(id string, cmd string, dir string) error {
	if len(id) == 0 {
		return fmt.Errorf("service name is empty")
	}
	cmdstrings := strings.Split(cmd, " ")
	runcmd := cmdstrings[0]
	args := cmdstrings[1:]

	if s.IsIDExist(id) {
		fmt.Printf("Updating id: %s cmd: %s dir: %s args: %v\n", id, cmd, dir, args)
		return s.UpdateCommand(id, runcmd, dir, args)
	} else {
		// add
		fmt.Printf("Adding id: %s cmd: %s dir: %s args: %v\n", id, cmd, dir, args)
		s.Commands = append(s.Commands, *NewSvcCommand(id, runcmd, dir, args))
	}

	return nil
}

// RunSvc runs a service
func (s *SvcCommands) RunSvc(wg *sync.WaitGroup, name string) error {
	for _, svc := range s.Commands {
		if svc.ID == name {
			go func() {
				defer wg.Done()

				s.Write([]byte(fmt.Sprintf("$ %s\n", svc.CmdStr())))

				runCmd := strings.Split(svc.CmdStr(), " ")[0]

				cmd := exec.Command(runCmd, strings.Split(svc.CmdStr(), " ")[1:]...)
				cmd.Stdout = s
				cmd.Stderr = s
				err := cmd.Start()
				if err != nil {
					s.Write([]byte(fmt.Sprintf("Failed to start application %s: %v\n", svc.ID, err)))
					return
				}
			}()
			return nil
		}
	}
	return fmt.Errorf("service %s not found", name)
}

// UpdateCommand updates the command of the service
func (s *SvcCommands) UpdateCommand(id string, cmd string, dir string, args []string) error {
	for i, svc := range s.Commands {
		if svc.ID == id {
			s.Commands[i].Path = dir
			s.Commands[i].Cmd = cmd
			s.Commands[i].Args = args
			return nil
		}
	}
	return fmt.Errorf("service %s not found", id)
}

// DeleteService deletes a service from the ShellCommands
func (s *SvcCommands) DeleteService(id string) error {
	for i, svc := range s.Commands {
		if svc.ID == id {
			s.Commands = append(s.Commands[:i], s.Commands[i+1:]...)
			return nil
		}
	}
	return fmt.Errorf("service %s not found", id)
}

type RootSvcOptions struct {
	License string `json:"license"`
	PrivKey string `json:"privkey"`
}

// AddRootSvc adds a root service to the ShellCommands
func (s *SvcCommands) AddRootSvc(name, exePath string, opts *RootSvcOptions) error {
	cmd := "bbrootsvc"
	if s.IsIDExist(name) {
		return fmt.Errorf("service %s already exists", name)
	}

	if len(name) == 0 {
		return fmt.Errorf("service name is empty")
	}

	args := []string{"-n", name}

	if opts != nil {
		licensePath := opts.License
		if len(licensePath) > 0 {
			args = append(args, "-license", licensePath)
		}
		privKeyPath := opts.PrivKey
		if len(privKeyPath) > 0 {
			args = append(args, "-privkey", privKeyPath)
		}
	}

	s.Commands = append(s.Commands, *NewSvcCommand(name, cmd, exePath, args))
	return nil
}

// AddBBSvc adds a Nimbl services to SvcCommands
func (s *SvcCommands) AddBBSvc(name string, exePath string, cmd string, port string) error {
	args := []string{"-n", name, "-r", s.RootURL}
	if len(port) > 0 {
		args = append(args, []string{"-p", port}...)
	}
	return s.AddService(name, exePath, cmd, args)
}

// RunAll runs all the services
func (s *SvcCommands) RunAll() error {
	var wg sync.WaitGroup
	// find root service, cmd is bbrootsvc
	var rootsvc string
	var others []string
	for _, svc := range s.Commands {
		if svc.Cmd == "bbrootsvc" {
			rootsvc = svc.ID
		} else {
			others = append(others, svc.ID)
		}
	}

	wg.Add(1)
	err := s.RunSvc(&wg, rootsvc)
	if err != nil {
		return err
	}

	// wait for rootsvc to start utils.HelloRoot returns true
	for {
		endpoint := "http://localhost:27182/api"
		resp, err := http.Get(endpoint)
		if err != nil {
			time.Sleep(3 * time.Second)
			continue
		}
		defer resp.Body.Close()

		if resp.StatusCode == 200 {
			break
		}

		s.Write([]byte("Waiting for rootsvc to start\n"))
		// sleep for 2 seconds
		time.Sleep(2 * time.Second)
	}

	for _, svc := range others {
		wg.Add(1)
		err = s.RunSvc(&wg, svc)
		if err != nil {
			return err
		}
	}
	wg.Wait()
	return nil
}

// ParseFlagN extracts the value associated with the -n flag from the command string.
func ParseFlagN(cmd string) (string, error) {
	args := strings.Fields(cmd)

	for i, arg := range args {
		if arg == "-n" {
			if i+1 < len(args) && !strings.HasPrefix(args[i+1], "-") {
				return args[i+1], nil
			}
			return "", errors.New("error: nil name")
		}
	}
	return "", errors.New("error: -n flag not found")
}

// Unzip extracts the contents of a zip file to the ./nimbl directory
func Unzip(src string) error {
	r, err := zip.OpenReader(src)
	if err != nil {
		return err
	}
	defer r.Close()

	// Create the ./nimbl directory
	os.MkdirAll("./nimbl", os.ModePerm)

	// dest := "./nimbl"
	dest := "./nimbl"

	for _, f := range r.File {
		fpath := filepath.Join(dest, f.Name)
		if !strings.HasPrefix(fpath, filepath.Clean(dest)+string(os.PathSeparator)) {
			return fmt.Errorf("illegal file path: %s", fpath)
		}

		if f.FileInfo().IsDir() {
			// Make Folder
			os.MkdirAll(fpath, os.ModePerm)
			continue
		}

		// Make File
		if err := os.MkdirAll(filepath.Dir(fpath), os.ModePerm); err != nil {
			return err
		}

		outFile, err := os.OpenFile(fpath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.Mode())
		if err != nil {
			return err
		}
		rc, err := f.Open()
		if err != nil {
			return err
		}

		_, err = io.Copy(outFile, rc)

		// Close the file without defer to close before next iteration of loop
		outFile.Close()
		rc.Close()

		if err != nil {
			return err
		}
	}
	return nil
}
