import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON>, Typography, Space, Collapse, App } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import { useToolCallPermitMutation } from "../../app/services/aiassistApi";

const { Text, Title } = Typography;
const { Panel } = Collapse;

const ConfirmToolCall = ({ sessionid, toolCalls, onFinish }) => {
  const [toolCallPermit] = useToolCallPermitMutation();
  const { notification } = App.useApp();
  const calls = Array.isArray(toolCalls) ? toolCalls : [];

  const handlePermit = async (tool) => {
    try {
      const response = await toolCallPermit({
        sessionid,
        toolcallid: tool.id,
      }).unwrap();
      console.log("handlePermit response", response);
    } catch (error) {
      notification.error({
        message: `Failed to permit tool "${tool.name}"`,
        description: error.message,
      });
      throw error;
    }
  };

  return (
    <Card
      style={{ maxWidth: 600, margin: "0 auto" }}
      title={
        <Space>
          <InfoCircleOutlined style={{ color: "#1890ff" }} />
          <Title level={5} style={{ margin: 0 }}>
            Tool Call Permission Required
          </Title>
        </Space>
      }
    >
      <Text type="secondary" style={{ display: "block", marginBottom: 16 }}>
        The assistant wants to call these tools. Review details before allowing:
      </Text>

      <Collapse>
        {calls.map((tool, idx) => (
          <Panel
            key={idx}
            header={
              <Text strong style={{ fontSize: 14 }}>
                {tool.name}
              </Text>
            }
          >
            <pre
              style={{
                background: "#f5f5f5",
                padding: 8,
                borderRadius: 4,
                fontSize: 12,
                overflowX: "auto",
              }}
            >
              {JSON.stringify(tool || {}, null, 2)}
            </pre>
          </Panel>
        ))}
      </Collapse>

      <div style={{ marginTop: 16, textAlign: "right" }}>
        <Space>
          <Button onClick={onFinish}>Cancel</Button>
          <Button
            type="primary"
            onClick={async () => {
              try {
                await Promise.all(calls.map((tool) => handlePermit(tool)));
                console.log("all tool calls permitted");
                onFinish();
              } catch (error) {
                console.error("Failed to permit tool calls:", error);
                notification.error({
                  message: "Failed to permit tool calls",
                  description: "Please try again.",
                });
              }
            }}
          >
            Allow
          </Button>
        </Space>
      </div>
    </Card>
  );
};

ConfirmToolCall.propTypes = {
  toolCalls: PropTypes.oneOfType([
    PropTypes.arrayOf(
      PropTypes.shape({
        name: PropTypes.string.isRequired,
        arguments: PropTypes.object,
      })
    ),
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      arguments: PropTypes.object,
    }),
  ]),
  sessionid: PropTypes.string.isRequired,
  onFinish: PropTypes.func.isRequired,
};

export default ConfirmToolCall;
