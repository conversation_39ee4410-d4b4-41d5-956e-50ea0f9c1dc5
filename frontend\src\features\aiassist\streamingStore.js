import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";

/**
 * @typedef {Object} StreamState
 * @property {string|null} sessionID - Current session ID
 * @property {any|null} final - Final response data
 * @property {Array} processing - Array of processing messages
 * @property {string|null} error - Error message if any
 * @property {boolean} isLoading - Loading state
 * @property {Array} toolCalls - Tool calls array
 */

export const useStreamingStore = create(
  persist(
    (set) => ({
      // Initial state
      sessionID: null,
      final: null,
      processing: [],
      error: null,
      isLoading: false,
      toolCalls: [],
      chatHistory: [],

      // Actions
      setSessionID: (sessionID) => set({ sessionID }),
      updateState: (updates) => set((state) => ({ ...state, ...updates })),
      resetState: () =>
        set({
          sessionID: null,
          final: null,
          processing: [],
          error: null,
          isLoading: false,
          toolCalls: [],
          chatHistory: [],
        }),
      resetToolCalls: () => set({ toolCalls: [] }),
      addToChatHistory: (message) =>
        set((state) => ({
          chatHistory: [...state.chatHistory, message],
        })),
    }),
    {
      name: "streaming-storage",
      storage: createJSONStorage(() => localStorage),
    }
  )
);
