# API Documentation
Below is a brief documentation of the API endpoints defined in the frontend.go file. Each endpoint is described with its HTTP method, path, and a short description of its purpose.

## General Services
- GET /api/v1/installerList
  - Description: Retrieves a list of available installers.
- GET /api/v1/status
  - Description: Checks the status of the server or service.
   
## Service Management
- GET /api/v1/services
  - Description: Fetches a list of all services.

- POST /api/v1/services
  - Description: Upserts (updates or inserts) services based on the provided data.

- DELETE /api/v1/services
  - Description: Deletes services based on the provided identifiers.
   
## Service Operations
- POST /api/v1/services/from_license
  - Description: Creates services based on a given license.
   
- POST /api/v1/services/run
  - Description: Executes a specific service.

- POST /api/v1/services/run/all
  - Description: Executes all available services.
   
- GET /api/v1/services/run
  - Description: Retrieves the running status of a specific service.

- DELETE /api/v1/services/run
  - Description: Terminates a running services.

- GET /api/v1/services/run/socket
  - Description: Establishes a socket connection for a running service.
  
- GET /api/v1/services/run/output
  - Description: Fetches the output of a running service.
   
## License and Key Management
- POST /api/v1/upload/license
  - Description: Uploads a license file.

- DELETE /api/v1/upload/license
  - Description: Deletes the uploaded license file.

- POST /api/v1/upload/privkey
  - Description: Uploads a private key file.

- DELETE /api/v1/upload/privkey
  - Description: Deletes the uploaded private key file.

## Installer Management
- POST /api/v1/downloadInstaller
  - Description: Initiates the download of an installer.

- POST /api/v1/uninstallNimbl
  - Description: Uninstalls the Nimbl application.

This documentation provides a high-level overview of each API endpoint. For detailed usage, including required parameters and response formats, further documentation or implementation details should be consulted.