package backend

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"slices"
	"strings"
	"sync"

	"mnms"
	"mnms/frontend/backend/utils"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

var DefaultLicensePath = "./nimbl/nmskey"

type BbackendState struct {
	Services     *utils.SvcCommands
	BinariesPath string // path to the binaries
}

var state *BbackendState

func init() {
	srvs := utils.NewServices(utils.DefaultRootURL)
	state = &BbackendState{
		Services: srvs,
	}
}

func (s *BbackendState) CheckNimblFiles() error {
	loc, err := utils.GetNimblFilesPath()
	if err != nil {
		return err
	}
	s.BinariesPath = loc
	return nil
}

// LoadLicenseFromDefaultPath loads the license from the default path
func LoadLicenseFromDefaultPath() (*mnms.NimblLicense, error) {
	// default licencse is ./nimbl/nmskey
	return mnms.LoadLicenseFile(DefaultLicensePath)
}

// HandleDeleteLicense delete the license file
func HandleDeleteLicense(c *gin.Context) {
	err := os.Remove(DefaultLicensePath)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "License file deleted successfully"})
}

// HandleLicenseUpload upload file to the ./nimbl
// Handle file upload
func HandleLicenseUpload(c *gin.Context) {
	// Retrieve the uploaded file
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to retrieve file"})
		return
	}

	// Ensure the ./nimbl folder exists
	err = os.MkdirAll("./nimbl", os.ModePerm)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create directory"})
		return
	}

	// Create the destination file path
	dst := filepath.Join("./nimbl", "nmskey")

	// Save the uploaded file to the destination path
	err = c.SaveUploadedFile(file, dst)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file"})
		return
	}

	// check license
	_, err = mnms.LoadLicenseFile(dst)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "License file is invalid"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "File uploaded successfully", "filePath": dst, "error": ""})
}

// HandleDeletePrivKey
func HandleDeletePrivKey(c *gin.Context) {
	err := os.Remove("./nimbl/privkey.pem")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Private key file deleted successfully"})
}

// HandlePrivKeyUpload
func HandlePrivKeyUpload(c *gin.Context) {
	// Retrieve the uploaded file
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to retrieve file"})
		return
	}

	// Ensure the ./nimbl folder exists
	err = os.MkdirAll("./nimbl", os.ModePerm)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create directory"})
		return
	}

	// Create the destination file path
	dst := filepath.Join("./nimbl", "privkey.pem")

	// Save the uploaded file to the destination path
	err = c.SaveUploadedFile(file, dst)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "File uploaded successfully", "filePath": dst, "error": ""})
}

// HandleRunnningServices return running services in the local machine
func HandleRunnningServices(c *gin.Context) {
	runningSvcs, err := utils.RunningSvcs()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"runningService": runningSvcs, "error": ""})
}

// HandleStatus return local machine Nimbl status, including
// - Have Nimbl installed
// - Running bb* service in the same machine
// - Have Nimbl license
func HandleStatus(c *gin.Context) {
	type licenseData struct {
		Valid bool               `json:"valid"`
		Data  *mnms.NimblLicense `json:"data"`
	}

	type response struct {
		NimbleInstalled bool        `json:"nimbleInstalled"`
		RunningServices []string    `json:"runningService"`
		License         licenseData `json:"license"`
		Error           string      `json:"error"`
	}

	nibleFilesErr := state.CheckNimblFiles()
	runningSvcs, err := utils.RunningSvcs()
	slices.Sort(runningSvcs)
	compactRunningSvc := slices.Compact(runningSvcs)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	licValid := true
	lic, err := LoadLicenseFromDefaultPath()
	if err != nil {
		licValid = false
	}

	c.JSON(http.StatusOK, response{
		NimbleInstalled: nibleFilesErr == nil,
		RunningServices: compactRunningSvc,
		License:         licenseData{Valid: licValid, Data: lic},
		Error:           "",
	})
	return
}

type ServicesResponse struct {
	ShellCommand string `json:"shellCommand"`
	utils.SvcCommand
}

func getServices() []ServicesResponse {
	var items []ServicesResponse
	svcs := state.Services.Items()
	for _, svc := range svcs {
		items = append(items, ServicesResponse{
			ShellCommand: svc.CmdStr(),
			SvcCommand:   svc,
		})
	}
	return items
}

// getServicesShellCmd
func getServicesShellCmd() []string {
	var items []string
	svcs := state.Services.Items()
	for _, svc := range svcs {
		items = append(items, svc.CmdStr())
	}
	return items
}

// HandleGetServices return the commands of the services
func HandleGetServices(c *gin.Context) {
	items := getServices()
	c.JSON(http.StatusOK, gin.H{"error": nil, "services": items})
}

// HandlePostServices upsert services
// Sample upsert root service request body
//
//	[{
//	  "name": "root",
//	  "cmd": "bbrootsvc -n root",
//	}]
func HandlePostServices(c *gin.Context) {
	type request struct {
		Name string `json:"name"`
		Cmd  string `json:"cmd"`
	}
	var req []request
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	for _, r := range req {
		loc := state.BinariesPath
		err := state.Services.UpsertService(r.Name, loc, r.Cmd)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{"error": nil, "message": "OK"})
}

// HandleDeleteServices delete a service
func HandleDeleteServices(c *gin.Context) {
	type request struct {
		Name string `json:"name"`
	}
	var req request
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	err := state.Services.DeleteService(req.Name)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	msg := fmt.Sprintf("Service %s deleted successfully\n", req.Name)
	c.JSON(http.StatusOK, gin.H{"error": nil, "message": msg})
}

// handle get file list
func HandleInstallerList(c *gin.Context) {
	ret, err := GetNimblFileList()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"fileList": ret, "error": err})
		return
	}
	c.JSON(http.StatusOK, gin.H{"fileList": ret, "error": err})
}

// handle download installer
func HandleDownloadInstaller(c *gin.Context) {
	type request struct {
		Filename string `json:"filename" binding:"required"`
	}
	var req request
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	// download file
	err := DownloadFile(req.Filename, "./nimbl.zip")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	} else {

		err := utils.Unzip("./nimbl.zip")
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "failes to unzip file - " + err.Error()})
			return
		}
	}
	err = state.CheckNimblFiles()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to check nimbl file - " + err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"msg": "file fownload done"})
}

// handle get file list
func HandleUninstallNimbl(c *gin.Context) {
	err := os.RemoveAll("./nimbl")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"error": err})
}

// GetNimblFileList send a request to https://nimbl.blackbeartechhive.com/api/v1/list
func GetNimblFileList() ([]string, error) {
	// GET https://nimbl.blackbeartechhive.com/api/v1/list
	type response struct {
		Files []struct {
			Name string `json:"name"`
			Type string `json:"type"`
		} `json:"files"`
	}

	ret := make([]string, 0)

	var res response
	// send a http request
	resp, err := http.Get("https://nimbl.blackbeartechhive.com/api/v1/list")
	if err != nil {
		return ret, err
	}
	defer resp.Body.Close()
	// read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return ret, err
	}

	// parse response
	err = json.Unmarshal(body, &res)
	if err != nil {
		return ret, err
	}

	// Search all file start with bbnim and type is zip
	for _, file := range res.Files {
		if strings.HasPrefix(file.Name, "bbnim") && file.Type == ".zip" {
			ret = append(ret, file.Name)
		}
	}

	return ret, nil
}

// DownloadFile download a file from https://nimbl.blackbeartechhive.com/api/v1/files/{filename}
func DownloadFile(filename string, filepath string) error {
	// create a file
	out, err := os.Create(filepath)
	if err != nil {
		return err
	}
	defer out.Close()

	// Get the data
	resp, err := http.Get("https://nimbl.blackbeartechhive.com/api/v1/files/" + filename)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("bad sataus %s", resp.Status)
	}

	fmt.Printf("Downloading %s, %d bytes\n", filename, resp.ContentLength)

	// TeeReader reads from r and writes to w, until either EOF is reached on r or an error occurs.
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return err
	}

	return nil
}

// CreateServicesFromLicense
func CreateServicesFromLicense(privkeyPath string) error {
	// check license file ./nimbl/nmskey exist
	licenseFile := true
	_, err := os.Stat("./nimbl/nmskey")
	if err != nil {
		licenseFile = false
	}

	opt := utils.RootSvcOptions{
		PrivKey: privkeyPath,
		License: func() string {
			if licenseFile {
				return "./nimbl/nmskey"
			}
			return ""
		}(),
	}
	loc := state.BinariesPath

	err = state.Services.AddRootSvc("root", loc, &opt)
	if err != nil {
		return fmt.Errorf("failed to add root service: %v", err)
	}
	// Add nmssvc

	err = state.Services.AddBBSvc("nms", loc, "bbnmssvc", "")
	if err != nil {
		return fmt.Errorf("failed to add nms service: %v", err)
	}

	// add log svc
	err = state.Services.AddBBSvc("log", loc, "bblogsvc", "")
	if err != nil {
		return fmt.Errorf("failed to add log service: %v", err)
	}

	if licenseFile {
		// validate license
		lic, err := mnms.LoadLicenseFile("./nimbl/nmskey")
		if err == nil {
			if lic.HasFeatureAnomalyDetection() {
				// Add anomaly
				err = state.Services.AddBBSvc("anom", loc, "bbanomsvc", "")
				if err != nil {
					return fmt.Errorf("failed to add anomaly service: %v", err)
				}
			}

			if lic.HasFeatureIdps() {
				// Add idps
				err = state.Services.AddBBSvc("idp", loc, "bbidpsvc", "")
				if err != nil {
					return fmt.Errorf("failed to add idps service: %v", err)
				}
			}
		}
	}

	return nil
}

// HandleCreateServicesFromLicense
func HandleCreateServicesFromLicense(c *gin.Context) {
	type request struct {
		PrivKeyPath string `json:"privKeyPath"`
	}
	var req request
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// if privkeyPath is empty, try to find the privkey.pem in the ./nimbl folder
	if req.PrivKeyPath == "" {
		_, err := os.Stat("./nimbl/privkey.pem")
		if err == nil {
			req.PrivKeyPath = "./nimbl/privkey.pem"
		}
	}

	// clear all services
	state.Services.ClearCommands()

	err := CreateServicesFromLicense(req.PrivKeyPath)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	srvs := getServices()

	c.JSON(http.StatusOK, gin.H{"error": nil, "created-services": srvs})
}

// HandlecreateBasicServices
// body : {}
func HandlecreateBasicServices(c *gin.Context) {
	loc := state.BinariesPath
	err := state.Services.AddRootSvc("root", loc, nil)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	// Add nmssvc

	err = state.Services.AddBBSvc("nms", loc, "bbnmssvc", "")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// add log svc
	err = state.Services.AddBBSvc("log", loc, "bblogsvc", "")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"error": nil, "message": "basic services created"})
}

// HandleServiceStream
func HandleRunServiceSocket(c *gin.Context) {
	upGrader := websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
	}
	ws, err := upGrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		fmt.Printf("Failed to upgrade: %v\n", err)
	}

	defer func() {
		closeSocketErr := ws.Close()
		if closeSocketErr != nil {
			fmt.Printf("Failed to close socket: %v\n", closeSocketErr)
		}
	}()

	// send message from state.Services.Notify: to client
	go func() {
		for {
			select {
			case msg := <-state.Services.Notify:
				fmt.Printf("msg := <-state.Services.Notify:\n")
				err := ws.WriteMessage(websocket.TextMessage, []byte(msg))
				if err != nil {
					fmt.Println("WriteMessage error:", err)
					return
				}
			case <-c.Request.Context().Done():
				fmt.Println("Client disconnected")
				return
			}
		}
	}()

	for {
		_, _, err := ws.ReadMessage()
		if err != nil {
			fmt.Println("ReadMessage error:", err)
			break
		}
	}
}

// HandleRunService run a service
// POST {"name": "root"}
func HandleRunService(c *gin.Context) {
	type request struct {
		Name string `json:"name"`
	}
	var req request
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	if state.Services.IsIDExist(req.Name) == false {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("service %s not found \n", req.Name)})
		return
	}
	var wg sync.WaitGroup
	wg.Add(1)
	err := state.Services.RunSvc(&wg, req.Name)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"error": nil, "message": fmt.Sprintf("service %s is running\n", req.Name)})
}

// HandleRunAllServices
func HandleRunAllServices(c *gin.Context) {
	err := state.Services.RunAll()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"error": nil, "message": "all services are running"})
}

// HandleGetRunServiceOutput
func HandleGetRunServiceOutput(c *gin.Context) {
	output := state.Services.Output.String()
	c.JSON(http.StatusOK, gin.H{"error": nil, "output": output})
}

// HandleGetRunService
func HandleGetRunService(c *gin.Context) {
	runningSvcs, err := utils.RunningSvcs()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"runningNimblServices": runningSvcs, "error": ""})
}

// HandleDeleteRunService
// {"services":["bbrootsvc"]}
func HandleDeleteRunService(c *gin.Context) {
	type request struct {
		Services []string `json:"services"`
	}
	var req request
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	err := utils.KillProcesses(req.Services)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"error": nil, "message": "services killed"})
}

// HandleDeleteAllRunServices
func HandleDeleteAllRunServices(c *gin.Context) {
	runningSvcs, err := utils.RunningSvcs()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	slices.Sort(runningSvcs)
	compactRunningSvc := slices.Compact(runningSvcs)
	err = utils.KillProcesses(compactRunningSvc)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"error": nil, "message": "all Nimbl services killed"})
}

// HandleGetShellCmd
func HandleGetShellCmd(c *gin.Context) {
	shellCmds := getServicesShellCmd()
	if len(shellCmds) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "no shell commands found"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"error": nil, "shellCommands": shellCmds})
}

// HandlePostShellCmd
// body" ["bbrootsvc -n root", "bbnmssvc -r http://localhost:27182 -n nms"]
// This API will clear existing services and insert new services which are in the request body
func HandlePostShellCmd(c *gin.Context) {
	var req []string
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	// clear all services
	// state.Services.ClearCommands()

	svcs := map[string]string{}
	// upert shell commands
	for _, cmd := range req {
		name, err := utils.ParseFlagN(cmd)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		svcs[name] = cmd
	}
	loc := state.BinariesPath
	// upsert shell commands
	for name, cmd := range svcs {
		fmt.Printf("name: %s, cmd: %s loc:%s\n", name, cmd, loc)
		err := state.Services.UpsertShellCommand(name, cmd, loc)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
	}
	c.JSON(http.StatusOK, gin.H{"error": nil, "message": "shell commands upserted"})
}
