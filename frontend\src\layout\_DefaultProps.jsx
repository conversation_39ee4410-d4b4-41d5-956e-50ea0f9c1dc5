import {
  ApartmentOutlined,
  ApiOutlined,
  ClusterOutlined,
  CodeOutlined,
  DashboardOutlined,
  DesktopOutlined,
  ExclamationCircleOutlined,
  GlobalOutlined,
  KeyOutlined,
  LaptopOutlined,
  MacCommandOutlined,
  SnippetsOutlined,
  UsergroupAddOutlined,
} from "@ant-design/icons";

const routes = ({ anomalies = false, idps = false }) => ({
  route: {
    path: "/",
    routes: [
      {
        path: "/dashboard",
        name: "Dashboard",
        icon: <DashboardOutlined />,
        routes: [
          { path: "/dashboard/device", name: "Device" },
          anomalies && { path: "/dashboard/anomalies", name: "Anomalies" },
          idps && { path: "/dashboard/idps", name: "IDPS" },
        ],
      },
      {
        path: "/devices",
        name: "Devices",
        icon: <DesktopOutlined />,
      },
      {
        path: "/scripts",
        name: "Scripts",
        icon: <CodeOutlined />,
      },
      {
        path: "/topology",
        name: "Topology",
        icon: <ApartmentOutlined />,
      },
      {
        path: "/mibbrowser",
        name: "<PERSON><PERSON> Browser",
        icon: <GlobalOutlined />,
      },

      {
        path: "/usermanagement",
        name: "User Management",
        icon: <UsergroupAddOutlined />,
      },
      {
        path: "/eventlogs",
        name: "Logs",
        icon: <SnippetsOutlined />,
      },
      {
        path: "/clusterinfo",
        name: "Cluster Info",
        icon: <ClusterOutlined />,
      },
      {
        path: "/tunnels",
        name: "Tunnel",
        icon: <ApiOutlined />,
      },
      {
        path: "/key-store",
        name: "Key Store",
        icon: <KeyOutlined />,
      },
    ],
  },
  location: {
    pathname: "/",
  },
});
export default routes;
