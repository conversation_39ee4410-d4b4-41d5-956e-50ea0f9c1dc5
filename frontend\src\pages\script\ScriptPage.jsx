import {
  A<PERSON>,
  <PERSON><PERSON>,
  Card,
  Checkbox,
  Col,
  Divider,
  Flex,
  Form,
  Input,
  List,
  Pagination,
  Row,
  Space,
  Tag,
  Upload,
} from "antd";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import ModalResult from "../../components/debug/ModalResult";
import {
  clearDebugCmdData,
  debugCmdSelector,
  GetDebugCommandResult,
  RequestDebugCommand,
  inputCommandChange,
  setCommandFlags,
  clearCommandFlags,
} from "../../features/debugPage/debugPageSlice";
import { convertToJsonObject } from "../../utils/comman/dataMapping";
import AiButton from "../../components/aiassistant/aibutton";
import { ChatSession } from "../../components/aiassistant/ChatSession";
import {
  SyncOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  UploadOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";

const ScriptPage = () => {
  const dispatch = useDispatch();
  const { modal, message } = App.useApp();
  const { cmdResponse, inputCommand, commandFlags } =
    useSelector(debugCmdSelector);
  const [cmdResult, setCmdResult] = useState(null);
  const [isSNMPCommand, setSNMPCommand] = useState(false);
  const [totalCountResult, setTotalCount] = useState(0);
  const [resultData, setResultData] = useState("");
  const [pageSize, setPageSize] = useState(10);
  const [pageNum, setPageNum] = useState(1);
  const [cValue, setCvalue] = useState("");
  const [totalPage, setTotalPage] = useState(5);
  const [totalPageError, setTotalPageError] = useState("");
  const [totalPageSelected, setTotalPageSelected] = useState(5);
  const [originaTotalCount, setOriginaTotalCount] = useState(0);

  const handleClickRequestCommand = () => {
    setCmdResult(null);

    const cmdJsonObject = convertToJsonObject(inputCommand, commandFlags);
    dispatch(RequestDebugCommand(cmdJsonObject));
  };

  const handleViewResult = (cValue) => {
    setCvalue(cValue);
    const paramObj = {
      cValue: encodeURIComponent(cValue),
      pageSize: pageSize,
      pageNum: pageNum,
      totalPage: totalPageSelected,
    };
    dispatch(GetDebugCommandResult(paramObj))
      .unwrap()
      .then((result) => {
        const cResult = Object.values(result);
        if (cResult.length > 0) {
          if (
            cResult[0].command.includes("snmp walk") ||
            cResult[0].command.includes("snmp bulk")
          ) {
            setSNMPCommand(true);
            //Extract totalCount and data from result
            const resultString = cResult[0].result;
            if (resultString) {
              const resultObj = JSON.parse(resultString);
              setOriginaTotalCount(resultObj.TotalCount);
              let paginationTotalCount = totalPage * pageSize;
              if (paginationTotalCount > resultObj.TotalCount) {
                setTotalCount(resultObj.TotalCount);
              } else {
                setTotalCount(paginationTotalCount);
              }
              setResultData(resultObj?.Data);
            }
          } else {
            setSNMPCommand(false);
          }

          if (
            cResult.some(
              (el) =>
                el.status === "" ||
                el.status === "running" ||
                el.status.includes("pending:") ||
                el.status.includes("info:")
            )
          ) {
            setTimeout(() => {
              handleViewResult(cValue);
            }, 5000);
          }
        }
        setCmdResult(cResult);
      })
      .catch((error) => {
        modal.error({
          title: "Command Result",
          content: error,
        });
      });
  };

  useEffect(() => {
    if (totalPageError === "") {
      if (cValue) {
        handleViewResult(cValue);
      }
    }
  }, [pageNum, pageSize, totalPageSelected]);

  const dummyRequest = async ({ file, onSuccess }) => {
    setTimeout(() => {
      onSuccess("ok");
    }, 0);
  };

  const uploadProps = {
    name: "cmdfile",
    multiple: false,
    accept: "text/plain",
    customRequest: dummyRequest,
    showUploadList: false,
    onChange({ file, fileList }) {
      if (file.status !== "uploading") {
        console.log(file, fileList);
      }
      if (file.status === "done") {
        message.success(`${file.name} file uploaded successfully`);
        const reader = new FileReader();
        reader.onload = async (e) => {
          const text = e.target.result;
          dispatch(inputCommandChange(text));
        };
        reader.readAsText(file.originFileObj);
      } else if (file.status === "error") {
        message.error(`${file.name} file upload failed.`);
      }
    },
  };

  useEffect(() => {
    return () => {
      dispatch(clearDebugCmdData());
      setCmdResult(null);
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const downloadFile = () => {
    const paramObj = {
      cValue: "all",
    };
    dispatch(GetDebugCommandResult(paramObj))
      .unwrap()
      .then((result) => {
        const fileName = "result" + dayjs(new Date());
        const json = JSON.stringify(result, null, 2);
        const blob = new Blob([json], { type: "application/json" });
        const href = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = href;
        link.download = fileName + ".json";
        document.body.appendChild(link);
        link.click();

        document.body.removeChild(link);
        URL.revokeObjectURL(href);
      })
      .catch((error) => {
        modal.error({
          title: "All Command Result",
          content: error,
        });
      });
  };
  const handlePagination = (pageNum, pageSize) => {
    setPageSize(pageSize);
    setPageNum(pageNum);
  };
  const handleTotalPage = (e) => {
    console.log(e.target.value);
    setTotalPage(e.target.value);
    if (e.target.value <= 0) {
      setTotalPageError("Minimum page size 1");
    } else {
      setTotalPageError("");
    }
  };
  const handleViewBtnClick = () => {
    let totalPageNo = parseInt(totalPage);
    let calTotalCount = totalPage * pageSize;
    if (calTotalCount > originaTotalCount) {
      let calPgNo = Math.ceil(originaTotalCount / pageSize);
      setPageNum(calPgNo);
      setTotalPageSelected(calPgNo);
    } else {
      setPageNum(totalPageNo);
      setTotalPageSelected(totalPageNo);
    }
  };

  //console.log(cmdResult);

  return (
    <Row gutter={[16, 16]}>
      <Col xs={24} md={24} lg={12}>
        <Card
          bordered={false}
          title="Request Script"
          extra={
            <Space>
              <AiButton value="cmd" />
              <Upload {...uploadProps}>
                <Button type="primary" icon={<UploadOutlined />}>
                  Upload CMDS Script
                </Button>
              </Upload>
              <Button
                type="primary"
                onClick={() => handleClickRequestCommand()}
              >
                run command
              </Button>
            </Space>
          }
        >
          <Input.TextArea
            rows={12}
            value={inputCommand}
            onChange={(e) => dispatch(inputCommandChange(e.target.value))}
          />
          <Divider orientation="left">Command Flags</Divider>
          <Flex gap={10} justify="space-between">
            <Form.Item
              label="client"
              tooltip="Enter the network service name to send the command to the specified network service."
            >
              <Input
                placeholder="-cc"
                value={commandFlags.cc}
                onChange={(e) =>
                  dispatch(
                    setCommandFlags({ field: "cc", value: e.target.value })
                  )
                }
              />
            </Form.Item>
            <Form.Item
              label="tag"
              tooltip="Add tag to the command. This will change the syslog program name style. The default is RunCmd."
            >
              <Input
                placeholder="-ct"
                value={commandFlags.ct}
                onChange={(e) =>
                  dispatch(
                    setCommandFlags({ field: "ct", value: e.target.value })
                  )
                }
              />
            </Form.Item>
            <Form.Item
              label="kind"
              tooltip="Send the command to the root service. Currently, you can only enter root."
            >
              <Input
                placeholder="-ck"
                value={commandFlags.ck}
                onChange={(e) =>
                  dispatch(
                    setCommandFlags({ field: "ck", value: e.target.value })
                  )
                }
              />
            </Form.Item>
          </Flex>
          <Flex gap={10} justify="space-between">
            {/* <Form.Item
              label="all"
              tooltip="Send the command to all network service."
            >
              <Checkbox
                checked={commandFlags.ca}
                onChange={(e) =>
                  dispatch(
                    setCommandFlags({ field: "ca", value: e.target.checked })
                  )
                }
              />
            </Form.Item> */}
            <Form.Item
              label="nooverwrite"
              tooltip="The command will not be overwritten, so the same command will not take effect again. You should be careful when using this flag."
            >
              <Checkbox
                checked={commandFlags.cno}
                onChange={(e) =>
                  dispatch(
                    setCommandFlags({ field: "cno", value: e.target.checked })
                  )
                }
              />
            </Form.Item>
            <Form.Item
              label="nosyslog"
              tooltip="This command will not send syslog."
            >
              <Checkbox
                checked={commandFlags.cns}
                onChange={(e) =>
                  dispatch(
                    setCommandFlags({ field: "cns", value: e.target.checked })
                  )
                }
              />
            </Form.Item>
          </Flex>

          <Button block onClick={() => dispatch(clearCommandFlags({}))}>
            clear command flags
          </Button>
        </Card>
      </Col>
      <Col xs={24} md={24} lg={12}>
        <Card
          bordered={false}
          title="Response Result"
          extra={
            <Space>
              <Button type="primary" onClick={downloadFile}>
                Download all results
              </Button>
            </Space>
          }
        >
          <List
            itemLayout="horizontal"
            dataSource={cmdResponse}
            renderItem={(item) => (
              <List.Item
                key={item}
                extra={
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => {
                      setResultData("");
                      setPageSize(10);
                      setTotalPage(5);
                      setTotalPageSelected(5);
                      setPageNum(1);
                      handleViewResult(item);
                    }}
                  >
                    View Result
                  </Button>
                }
                style={{
                  display: "flex",
                  alignItems: "flex-start",
                  overflowX: "scroll",
                }}
              >
                <List.Item.Meta
                  title={item}
                  description={
                    Array.isArray(cmdResult) && cmdResult.length !== 0 ? (
                      <div>
                        {cmdResult.map(
                          (v) =>
                            v.command === item && (
                              <Space
                                direction="vertical"
                                style={{ width: "100%" }}
                              >
                                <ModalResult
                                  title="Command:"
                                  value={v.command}
                                />
                                <ModalResult
                                  title="Service Name:"
                                  value={v.client !== "" ? v.client : v.name}
                                />
                                <ModalResult
                                  title="Status:"
                                  value={
                                    v.status === "" ||
                                    v.status === "running" ||
                                    v.status.includes("pending:") ? (
                                      <Tag
                                        icon={<SyncOutlined spin />}
                                        color="processing"
                                      >
                                        processing
                                      </Tag>
                                    ) : v.status === "ok" ? (
                                      <Tag
                                        icon={<CheckCircleOutlined />}
                                        color="success"
                                      >
                                        ok
                                      </Tag>
                                    ) : v.status.includes("info:") ? (
                                      <Tag
                                        icon={<InfoCircleOutlined />}
                                        color="#108ee9"
                                      >
                                        {v.status}
                                      </Tag>
                                    ) : (
                                      <Tag
                                        icon={<CloseCircleOutlined />}
                                        color="error"
                                      >
                                        {v.status}
                                      </Tag>
                                    )
                                  }
                                />
                                <ModalResult
                                  title="Result:"
                                  value={
                                    v.result !== "" ? (
                                      isSNMPCommand ? (
                                        <>{resultData}</>
                                      ) : (
                                        v.result
                                      )
                                    ) : (
                                      "NO result assigned"
                                    )
                                  }
                                />
                                <ModalResult
                                  value={
                                    resultData !== "" &&
                                    isSNMPCommand && (
                                      <>
                                        <Pagination
                                          defaultCurrent={1}
                                          total={totalCountResult}
                                          onChange={handlePagination}
                                        />
                                        <div
                                          style={{
                                            display: "flex",
                                            justifyContent: "end",
                                            marginTop: "10px",
                                          }}
                                        >
                                          Total Page:
                                          <Input
                                            type="number"
                                            value={totalPage}
                                            placeholder="Page no."
                                            onChange={handleTotalPage}
                                            style={{
                                              width: "4.5rem",
                                              height: "1.6rem",
                                              marginRight: "10px",
                                              marginLeft: "5px",
                                            }}
                                          />
                                          <Button
                                            type="primary"
                                            size="small"
                                            onClick={handleViewBtnClick}
                                          >
                                            View
                                          </Button>
                                        </div>
                                        <h5
                                          style={{
                                            color: "red",
                                            display: "flex",
                                            justifyContent: "end",
                                            marginTop: "6px",
                                          }}
                                        >
                                          {totalPageError}
                                        </h5>
                                      </>
                                    )
                                  }
                                />
                                <Divider />
                              </Space>
                            )
                        )}
                      </div>
                    ) : (
                      <Space direction="vertical" style={{ width: "100%" }}>
                        <ModalResult
                          title="Result:"
                          value="NO result assigned"
                        />
                      </Space>
                    )
                  }
                />
              </List.Item>
            )}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default ScriptPage;
