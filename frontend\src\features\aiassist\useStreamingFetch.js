import { useRef, useCallback } from "react";
import protectedApi from "../../utils/apis/protectedApis";
import { useStreamingStore } from "./streamingStore";

/**
 * Custom hook for handling streaming API calls with support for cancellation and progress tracking
 * @returns {Object} Hook methods and state
 */
export function useStreamingFetchV2() {
  const abortControllerRef = useRef(null);

  const {
    sessionID,
    final,
    processing,
    error,
    isLoading,
    toolCalls,
    updateState,
    resetState,
    resetToolCalls,
    addToChatHistory,
  } = useStreamingStore();

  const stopStreaming = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      updateState({ isLoading: false });
    }
  }, [updateState]);

  const resetSession = useCallback(() => {
    resetState();
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, [resetState]);

  /**
   * Processes a single line of streaming response
   * @param {string} line - JSON string to process
   */
  const processStreamLine = useCallback(
    (line) => {
      if (!line.trim()) return;

      try {
        const json = JSON.parse(
          typeof line === "string" ? line : JSON.stringify(line)
        );

        if (json.sessionid && !sessionID) {
          updateState({ sessionID: json.sessionid });
        }

        if (json.error || (json.status && json.status.startsWith("error:"))) {
          updateState({
            error: json.error || json.status,
            isLoading: false,
            processing: [...processing, json.error || json.status],
          });
          return;
        }

        const handlers = {
          final: () => {
            updateState({
              final: json.data,
              isLoading: false,
            });
            if (json.data !== "") {
              addToChatHistory({ type: "assistant", content: json.data });
            }
          },
          error: () => updateState({ error: json.data, isLoading: false }),
          tool_call_permit: () =>
            updateState({ toolCalls: json.tool_calls || [] }),
          info: () => updateState({ processing: [...processing, json.data] }),
        };

        const handler = handlers[json.type] || handlers.info;
        handler();
      } catch (e) {
        console.error("Failed to parse JSON:", line, e);
        if (line.includes("[object Object]")) {
          console.warn(
            "Received [object Object] string instead of JSON. Check server response serialization."
          );
        }
      }
    },
    [processing, sessionID, updateState]
  );

  const startStreaming = useCallback(
    async (url, options = {}) => {
      updateState({
        isLoading: true,
        final: null,
        error: null,
        toolCalls: [],
      });

      const controller = new AbortController();
      abortControllerRef.current = controller;

      try {
        await protectedApi({
          url,
          method: options.method || "GET",
          data: options.body,
          headers: options.headers || {},
          signal: controller.signal,
          responseType: "text",
          onDownloadProgress: (progressEvent) => {
            const rawData =
              progressEvent.event?.currentTarget?.response ||
              progressEvent.currentTarget?.response ||
              progressEvent.target?.response ||
              progressEvent.response ||
              "";

            if (rawData) {
              rawData.split("\n").forEach(processStreamLine);
            }
          },
        });

        updateState({ isLoading: false });
      } catch (err) {
        if (err.name === "CanceledError" || err.name === "AbortError") {
          console.log("Streaming aborted by the user.");
        } else {
          console.error("Stream error:", err);
          updateState({ error: err, isLoading: false });
        }
      }
    },
    [processStreamLine, updateState]
  );

  return {
    sessionID,
    final,
    processing,
    error,
    isLoading,
    toolCalls,
    startStreaming,
    stopStreaming,
    resetSession,
    resetToolCalls,
  };
}
