import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  showDrawer: false,
  assistantResponses: [],
  isLoading: false,
  error: null,
  flow: "default",
  showChatSession: false,
};

export const aiassistSlice = createSlice({
  name: "aiassist",
  initialState,
  reducers: {
    showAIAssistDrawer: (state) => {
      state.showDrawer = true;
    },
    hideAIAssistDrawer: (state) => {
      state.showDrawer = false;
    },
    toggleAIAssistDrawer: (state) => {
      state.showDrawer = !state.showDrawer;
    },
    setAssistantLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    addAssistantResponse: (state, action) => {
      state.assistantResponses.push(action.payload);
    },
    clearAssistantResponses: (state) => {
      state.assistantResponses = [];
    },
    setAssistantError: (state, action) => {
      state.error = action.payload;
    },
    setAssistantFlow: (state, action) => {
      state.flow = action.payload;
    },
    showChatSession: (state) => {
      state.showChatSession = true;
    },
    hideChatSession: (state) => {
      state.showChatSession = false;
    },
  },
});

// Export actions
export const {
  showAIAssistDrawer,
  hideAIAssistDrawer,
  toggleAIAssistDrawer,
  setAssistantLoading,
  addAssistantResponse,
  clearAssistantResponses,
  setAssistantError,
  setAssistantFlow,
  showChatSession,
  hideChatSession,
} = aiassistSlice.actions;

// Selectors
export const selectShowDrawer = (state) => state.aiassist.showDrawer;
export const selectAssistantResponses = (state) =>
  state.aiassist.assistantResponses;
export const selectAssistantLoading = (state) => state.aiassist.isLoading;
export const selectAssistantError = (state) => state.aiassist.error;
export const selectAssistantFlow = (state) => state.aiassist.flow;
export const selectShowChatSession = (state) => state.aiassist.showChatSession;
export default aiassistSlice.reducer;
