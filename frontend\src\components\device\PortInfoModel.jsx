import {
  App,
  <PERSON>,
  Col,
  Flex,
  Mo<PERSON>,
  Row,
  Table,
  theme,
  Typography,
} from "antd";
import Icon, { CheckCircleOutlined, StopOutlined } from "@ant-design/icons";
import React, { useCallback, useEffect, useState } from "react";
import {
  useGetPortInfoQuery,
  useSendCommandMutation,
} from "../../app/services/commandApi";
import { MdFlashOff, MdFlashOn } from "react-icons/md";
import RealtimeTraffic from "./RealtimeTraffic";
import { useTheme } from "antd-style";
import NmsTableContextMenu from "../NmsTableContextMenu";

const columns = (func, token) => [
  {
    title: "PortName",
    dataIndex: "portName",
    width: 100,
    key: "portName",
    fixed: "left",
  },
  {
    title: "PortStatus",
    width: 100,
    dataIndex: "portStatus",
    key: "portStatus",
    render: (data, record) => (data ? "Up" : "Down"),
  },
  {
    title: "Speed",
    width: 100,
    dataIndex: "speed",
    key: "speed",
  },
  {
    title: "PortMode",
    width: 100,
    dataIndex: "portMode",
    key: "portMode",
  },
  {
    title: "InOctets",
    width: 150,
    dataIndex: "inOctets",
    key: "inOctets",
  },
  {
    title: "InErrors",
    width: 150,
    dataIndex: "inErrors",
    key: "inErrors",
  },
  {
    title: "InUcastPkts",
    width: 150,
    dataIndex: "inUcastPkts",
    key: "inUcastPkts",
  },
  {
    title: "InMulticastPkts",
    width: 150,
    dataIndex: "inMulticastPkts",
    key: "inMulticastPkts",
  },
  {
    title: "InBroadcastPkts",
    width: 150,
    dataIndex: "inBroadcastPkts",
    key: "inBroadcastPkts",
  },
  {
    title: "OutOctets",
    width: 150,
    dataIndex: "outOctets",
    key: "outOctets",
  },
  {
    title: "OutErrors",
    width: 150,
    dataIndex: "outErrors",
    key: "outErrors",
  },
  {
    title: "OutUcastPkts",
    width: 150,
    dataIndex: "outUcastPkts",
    key: "outUcastPkts",
  },
  {
    title: "OutMulticastPkts",
    width: 150,
    dataIndex: "outMulticastPkts",
    key: "outMulticastPkts",
  },
  {
    title: "OutBroadcastPkts",
    dataIndex: "outBroadcastPkts",
    width: 150,
    key: "outBroadcastPkts",
  },
  {
    title: "Status",
    width: 120,
    fixed: "right",
    key: "enableStatus",
    align: "center",
    render: (text, record) => (
      <>
        {record.enableStatus ? (
          <CheckCircleOutlined style={{ color: token.colorSuccess }} />
        ) : (
          <StopOutlined style={{ color: token.colorError }} />
        )}
      </>
    ),
  },
];

const infoItem = [
  { label: "Model Name", id: "modelname" },
  { label: "IP Address", id: "ipaddress" },
  { label: "MAC Address", id: "mac" },
  { label: "Kernel", id: "kernel" },
  { label: "Power", id: "power" },
];

const defultChartConfig = {
  series: [
    {
      name: "in traffic data",
      data: [],
    },
    {
      name: "out traffic data",
      data: [],
    },
  ],
  options: {
    chart: {
      height: 400,
      type: "line",
      toolbar: {
        show: false,
      },
      zoom: {
        enabled: false,
      },
    },
    colors: ["#1677ff", "#ff4d4f"],
    dataLabels: {
      enabled: false,
    },
    stroke: {
      curve: "smooth",
    },
    xaxis: {
      type: "text",
      title: {
        text: "Time",
      },
    },
    yaxis: {
      title: {
        text: "Byte",
      },
    },
    legend: {
      show: true,
      position: "top",
    },
  },
};

const tempLabels = ["", "", "", "", "", "", "", "", ""];
const tempdata = [null, null, null, null, null, null, null, null, null];

const PortInfoModel = ({ open, onCancel, recordData }) => {
  const [xPos, setXPos] = useState(0);
  const [yPos, setYPos] = useState(0);
  const [showMenu, setShowMenu] = useState(false);
  const [contextRecord, setContextRecord] = useState({});

  const { token } = theme.useToken();
  const { notification } = App.useApp();
  const { appearance } = useTheme();
  const [sendCommand, {}] = useSendCommandMutation();
  const { data, isLoading } = useGetPortInfoQuery(
    {},
    { pollingInterval: 10000 }
  );

  const [inData, setInData] = useState(tempdata);
  const [outData, setOutData] = useState(tempdata);
  const [category, setCategory] = useState(tempLabels);
  const [selectedPort, setSelectedPort] = useState("");
  const [trafficData, setTrafficData] = useState([]);

  const [lineChartState, setLineChartState] = useState(defultChartConfig);

  useEffect(() => {
    setLineChartState((prev) => ({
      ...prev,
      options: {
        ...prev.options,
        theme: { mode: appearance },
        chart: { ...prev.options.chart, background: token.colorBgContainer },
      },
    }));
  }, [token, appearance]);

  useEffect(() => {
    const portData =
      data && data[recordData?.mac]?.portStatus
        ? data[recordData?.mac]?.portStatus
        : [];
    if (portData.length > 0) {
      const filteredPortData = portData.filter(
        (item) => item.portName === selectedPort
      );
      if (trafficData.length > 0) {
        const prevTrafficData = trafficData[0];
        const currentData = filteredPortData[0];
        const now = new Date();
        const hour = String(now.getHours()).padStart(2, "0");
        const minute = String(now.getMinutes()).padStart(2, "0");
        const second = String(now.getSeconds()).padStart(2, "0");
        const currentCat = `${hour}:${minute}:${second}`;

        setLineChartState((prev) => ({
          ...prev,
          series: [
            {
              name: "In traffic data",
              data: [
                currentData.inOctets - prevTrafficData.inOctets,
                ...inData,
              ],
            },
            {
              name: "Out traffic data",
              data: [
                currentData.outOctets - prevTrafficData.outOctets,
                ...outData,
              ],
            },
          ],
          options: {
            ...prev.options,
            xaxis: {
              categories: [currentCat, ...category],
            },
          },
        }));
        setInData((prev) =>
          [currentData.inOctets - prevTrafficData.inOctets, ...prev].slice(0, 9)
        );
        setOutData((prev) =>
          [currentData.outOctets - prevTrafficData.outOctets, ...prev].slice(
            0,
            9
          )
        );
        setCategory((prev) => [currentCat, ...prev].slice(0, 9));
        setTrafficData(filteredPortData);
      } else {
        setTrafficData(filteredPortData);
      }
    }
  }, [data, selectedPort]);

  const handleContextMenuClick = (key, data) => {
    const { mac, portName } = data;
    switch (key) {
      case "enable":
        handlePortSwitchChange(mac, portName, 1);
        break;
      case "disable":
        handlePortSwitchChange(mac, portName, 0);
        break;
      default:
        break;
    }
  };

  const handlePortSwitchChange = async (mac, port, enable) => {
    try {
      const command = [
        {
          command: `agent config port enable ${mac} ${port} ${enable}`,
        },
      ];

      await sendCommand(command).unwrap();
      notification.success({
        message: `command sent for ${
          enable === 1 ? "enable" : "disable"
        } ${port}!`,
      });
    } catch (error) {
      notification.error({
        message:
          error.data.error || error.data || error || "somthing went wrong!",
      });
    }
  };

  const handleResetState = () => {
    setLineChartState(defultChartConfig);
    setInData(tempdata);
    setOutData(tempdata);
    setCategory(tempLabels);
    setTrafficData([]);
  };

  const handleContextMenu = useCallback(
    (e) => {
      e.preventDefault();
      setXPos(e.pageX - 30);
      setYPos(e.pageY - 210);
      setShowMenu(true);
    },
    [setXPos, setYPos]
  );

  const handleClick = useCallback(() => {
    showMenu && setShowMenu(false);
  }, [showMenu]);

  useEffect(() => {
    document.addEventListener("click", handleClick);
    return () => {
      document.addEventListener("click", handleClick);
    };
  });

  return (
    <Modal
      open={open}
      width="100%"
      style={{
        top: 20,
      }}
      destroyOnClose
      forceRender
      maskClosable={false}
      title="Device Port and Power Information"
      cancelText="Cancel"
      footer={null}
      onCancel={() => {
        handleResetState();
        setSelectedPort("");
        onCancel();
      }}
    >
      {data && (
        <Row gutter={[10, 10]}>
          <Col xs={24} md={6}>
            <Card styles={{ body: { height: 500 } }} title="Device Information">
              {infoItem.map((item) => (
                <Flex vertical key={item.id}>
                  <Typography.Title
                    align="center"
                    level={5}
                    style={{ backgroundColor: token.colorBgLayout }}
                  >
                    {`${item.label}`}
                  </Typography.Title>
                  {item.id === "power" ? (
                    <div>
                      {data[recordData.mac]?.powerStatus.map((pitem) => (
                        <div key={pitem.powerId} className="power-item">
                          <Typography.Title
                            align="center"
                            key={pitem.powerId}
                            level={5}
                          >
                            {`Power${pitem.powerId}  `}
                          </Typography.Title>
                          {pitem.status ? (
                            <Icon component={MdFlashOn} />
                          ) : (
                            <Icon component={MdFlashOff} />
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <Typography.Title
                      style={{ marginTop: 0 }}
                      align="center"
                      level={5}
                    >{`${recordData[item.id]}`}</Typography.Title>
                  )}
                </Flex>
              ))}
            </Card>
          </Col>
          <Col xs={24} md={18}>
            <RealtimeTraffic
              portData={data[recordData?.mac]?.portStatus || []}
              selectedPort={selectedPort}
              setSelectedPort={(value) => {
                handleResetState();
                setSelectedPort(value);
              }}
              lineChartState={lineChartState}
            />
          </Col>
          <Col xs={24} md={24}>
            <Table
              loading={isLoading}
              size="small"
              rowKey="portName"
              columns={columns(handlePortSwitchChange, token)}
              dataSource={data[recordData.mac]?.portStatus || []}
              scroll={{ x: 2021, y: 406 }}
              pagination={false}
              onRow={(record, rowIndex) => {
                return {
                  onContextMenu: async (event) => {
                    if (record) {
                      setContextRecord({ ...record, mac: recordData?.mac });
                      handleContextMenu(event);
                    }
                  },
                };
              }}
            />
            <NmsTableContextMenu
              position={{ showMenu, xPos, yPos }}
              menuItems={[
                contextRecord &&
                  contextRecord.enableStatus && {
                    label: "Disable Port",
                    key: "disable",
                  },
                contextRecord &&
                  !contextRecord.enableStatus && {
                    label: "Enable Port",
                    key: "enable",
                  },
              ]}
              record={contextRecord}
              onMenuClick={handleContextMenuClick}
            />
          </Col>
        </Row>
      )}
    </Modal>
  );
};

export default PortInfoModel;
