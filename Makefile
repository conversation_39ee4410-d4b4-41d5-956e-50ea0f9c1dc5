# Makefile for building and releasing bbnim

.PHONY: default help clean deep_clean install_build_environment install_system_packages \
        install_installbuilder \
        get_version setup_release_dirs \
        get_external_libs get_caddy_binaries build_user_manual \
        pack tag_release_prompt release release_final \
        clean_temporary_files \
        bbrootsvc bbnmssvc bbctl bblogsvc bbidpsvc frontend dev devcmds \
        lint release_build_bb release_build_get-machine-id install_doc

# --- Variables ---
EXE=
ifeq ($(OS),Windows_NT)
EXE=.exe
endif

RELEASE_DIR = release
LINUX_DIR = $(RELEASE_DIR)/linux_amd64
WINDOWS_DIR = $(RELEASE_DIR)/windows_amd64

# Go build flags for all binaries, VERSION will be set dynamically via .version.tmp
GO_BUILD_FLAGS = -ldflags "-X main.Version=$(VERSION) -s -w"

# Paths for cross-compilation with MinGW and Hyperscan/PCRE2 from mnms_installation
# CURRENTLY ASSUMES mnms_installation (and pcre2-windows) IS IN THE USER'S HOME DIRECTORY
WCGO_CFLAGS=-I$(CURDIR)/libs/mnms_installation/windows/include/hs -I$(CURDIR)/libs/mnms_installation/pcre2-windows/include
WCGO_LDFLAGS=-L$(CURDIR)/libs/mnms_installation/windows -lhs -L$(CURDIR)/libs/mnms_installation/pcre2-windows/lib -lpcre2-8

# List of Go services/commands to build
BBCMDS=frontend bbrootsvc bbctl bblogsvc bbnmssvc bbidpsvc
DEVCMDS=bbrootsvc bbctl bblogsvc bbnmssvc bbidpsvc

# --- Go Build Commands (retained from original Makefile) ---
define go-build
- mkdir -p $(2)
GOOS=$(1) go build $(GO_BUILD_FLAGS) -o $(2)/$(3) $(4)
chmod +x $(2)/$(3)
endef

# --- Primary Build Targets ---
default: $(BBCMDS)

devcmds: $(DEVCMDS)

dev:
	@if [ ! -d "./dist" ]; then\
    	make;\
	else\
		make devcmds;\
	fi

lint:
	go vet
	revive
	golangci-lint run
	staticcheck

frontend:
	# Note: VERSION is passed to sub-make for frontend.
	# If you want this to pick up the release VERSION, ensure it's set in the env.
	VERSION=$(VERSION) DISTRIBUTED=true make -C frontend

wgclient:
	make -C wgclient

bbrootsvc:
	make -C bbrootsvc

bbctl:
	make -C bbctl

bblogsvc:
	make -C bblogsvc

bbnmssvc:
	make -C bbnmssvc

bbidpsvc:
	make -C bbidpsvc


install_doc:
	@echo "Generating API documentation..."
	mkdir -p $(RELEASE_DIR)
	go doc -all > $(RELEASE_DIR)/API.txt
	# Ensure MANIFEST files are actually in the 'doc' folder if using 'doc/' prefix
	cp doc/README.md doc/authentication.md CHANGELOG.md COPYRIGHT.txt LICENSE.txt doc/MANIFESTForLinux.md doc/MANIFESTForWindows.md $(RELEASE_DIR)

release_build_get-machine-id:
	@echo "Building get-machine-id..."
	$(call go-build,linux,$(LINUX_DIR),get-machine-id,get-machine-id/main.go)
	$(call go-build,windows,$(WINDOWS_DIR),get-machine-id.exe,get-machine-id/main.go)

# release_build_bb depends on get_external_libs to ensure mnms_installation is available
release_build_bb: get_external_libs frontend
	@echo "Building all core Go services..."
	# Read VERSION from temporary file for this target
	$(eval VERSION := $(shell cat .version.tmp))
	for target in $(DEVCMDS); do \
		echo "  Building $$target with version $(VERSION)"; \
		GOOS=linux go build -ldflags "-X main.Version=$(VERSION) -s -w" -o $(LINUX_DIR)/$$target $$target/*.go; \
		CGO_CFLAGS='$(WCGO_CFLAGS)'  CGO_LDFLAGS='$(WCGO_LDFLAGS)' \
		GOOS=windows GOARCH=amd64 CGO_ENABLED=1 CXX=x86_64-w64-mingw32-g++ CC=x86_64-w64-mingw32-gcc go build $(GO_BUILD_FLAGS) -o $(WINDOWS_DIR)/$$target.exe $$target/*.go; \
	done

# --- Release Orchestration (Main target) ---
release: get_version setup_release_dirs \
		 install_build_environment \
         get_caddy_binaries \
         build_user_manual \
         release_build_bb release_build_get-machine-id install_doc \
         pack tag_release_prompt \
         clean_temporary_files
	@echo "---------------------------------------------------"
	@echo "Release process complete!"
	@echo "You can find installers in: $(RELEASE_DIR)"
	@echo "Example: scp -P 64422 root@testbed:/root/mnms/release/bbnim.zip ."
	@echo "---------------------------------------------------"

# --- Helper Targets (Called by 'release' or manually) ---

# This target sets up the full build environment. It should be run manually once per new environment,
# after 'sudo make install_system_packages' has been executed.
install_build_environment: install_installbuilder get_external_libs
	@echo "------------------------------------------------------------------"
	@echo "Build environment setup (non-sudo parts) complete."
	@echo "IMPORTANT: Ensure you have run 'sudo make install_system_packages' first."
	@echo "------------------------------------------------------------------"

# This target installs system packages. It MUST be run with sudo, separately.
install_system_packages:
	@echo "--------------------------------------------------------"
	@echo "WARNING: This target REQUIRES 'sudo'."
	@echo "         Run it manually: 'sudo make install_system_packages'."
	@echo "--------------------------------------------------------"
	@echo "Updating Go modules..."
	go mod tidy
	@echo "Installing build dependencies via apt-get..."
	sudo apt-get update && sudo apt-get install -y \
		libpcap-dev libnetfilter-queue-dev libhyperscan-dev \
		libpcre2-8-0 libpcre2-dev mingw-w64 upx zip unzip nsis nodejs npm
	@echo "System packages check/install complete."

get_version:
	@read -p "Enter version (ex: v1.0.0): " VERSION_INPUT; \
	if [ -z "$$VERSION_INPUT" ]; then \
		echo "No version, exiting" && exit 1; \
	fi; \
	echo "$$VERSION_INPUT" > .version.tmp; \
	echo "Version set to: $$VERSION_INPUT";

setup_release_dirs:
	@echo "Setting up release directories..."
	mkdir -p $(LINUX_DIR)
	mkdir -p $(WINDOWS_DIR)

# install_installbuilder now installs to a project-local directory, no sudo needed
install_installbuilder:
	@echo "Checking/Installing InstallBuilder..."
	BUILDER_INSTALL_DIR=$(CURDIR)/tools/installbuilder; \
	mkdir -p $$BUILDER_INSTALL_DIR; \
	if [ ! -d "$$BUILDER_INSTALL_DIR/bin" ]; then \
		echo "InstallBuilder not found, installing to $$BUILDER_INSTALL_DIR..."; \
		curl --fail -sSL https://releases.installbuilder.com/installbuilder/installbuilder-enterprise-25.3.1-linux-x64-installer.run -o $$BUILDER_INSTALL_DIR/installbuilder.run; \
		chmod a+x $$BUILDER_INSTALL_DIR/installbuilder.run; \
		$$BUILDER_INSTALL_DIR/installbuilder.run --mode unattended --prefix "$$BUILDER_INSTALL_DIR"; \
		rm $$BUILDER_INSTALL_DIR/installbuilder.run; \
	else \
		echo "InstallBuilder already installed at $$BUILDER_INSTALL_DIR."; \
	fi; \
	echo "InstallBuilder version check:"; \
	PATH="$$BUILDER_INSTALL_DIR/bin:$$PATH" builder --version

get_external_libs:
	@echo "Cloning mnms_installation repository..."
	# Clone mnms_installation into a 'libs' directory within the current project
	mkdir -p $(CURDIR)/libs
	# Clone only if not already present
	[ -d "$(CURDIR)/libs/mnms_installation" ] || <NAME_EMAIL>:bbtechhive/mnms_installation.git $(CURDIR)/libs/mnms_installation
	@echo "Copying external libraries and installers..."
	cp $(CURDIR)/libs/mnms_installation/linux/install_bbnim_lib_amd64.sh $(LINUX_DIR)/install_lib.sh
	cp $(CURDIR)/libs/mnms_installation/windows/npcap-1.72.exe $(WINDOWS_DIR)
	cp -r $(CURDIR)/libs/mnms_installation/windows/x64/* $(WINDOWS_DIR)
	# No 'mv mnms_installation $(HOME)/mnms_installation' - it stays in $(CURDIR)/libs

get_caddy_binaries:
	@echo "Downloading Caddy binaries..."
	# Use -N to prevent re-downloading if file exists and is recent
	wget -N https://github.com/caddyserver/caddy/releases/download/v2.6.3/caddy_2.6.3_windows_amd64.zip
	mkdir -p /tmp/caddy_windows_amd64
	unzip caddy_2.6.3_windows_amd64.zip -d /tmp/caddy_windows_amd64
	cp /tmp/caddy_windows_amd64/caddy.exe $(WINDOWS_DIR)
	rm -rf caddy_2.6.3_windows_amd64.zip /tmp/caddy_windows_amd64

	wget -N https://github.com/caddyserver/caddy/releases/download/v2.6.3/caddy_2.6.3_linux_amd64.tar.gz
	mkdir -p /tmp/caddy_linux_amd64
	tar -xzf caddy_2.6.3_linux_amd64.tar.gz -C /tmp/caddy_linux_amd64
	cp /tmp/caddy_linux_amd64/caddy $(LINUX_DIR)
	rm -rf caddy_2.6.3_linux_amd64.tar.gz /tmp/caddy_linux_amd64

build_user_manual:
	@echo "Building user manual (PDF)..."
	# Clone only if not already present
	[ -d "userguide" ] || <NAME_EMAIL>:bbtechhive/userguide.git
	npm install mdpdf # Install mdpdf locally (no sudo needed)
	python userguide/merge_markdown.py
	npx mdpdf userguide/user_guide.md # Use npx to run locally installed mdpdf
	mv userguide/user_guide.pdf $(RELEASE_DIR)/Blackbear_NIMBL_User_Manual.pdf

pack: install_installbuilder # Ensure InstallBuilder is installed before packing
	@echo "Creating installers with InstallBuilder..."
	# Read VERSION from temporary file for this target
	$(eval VERSION := $(shell cat .version.tmp))
	# All shell commands that need the same PATH context MUST be on one logical line.
	# Define BUILDER_BIN_PATH as a shell variable here.
	# Then set PATH for the builder commands.
	# BUILDER_BIN_PATH=$(CURDIR)/tools/installbuilder/bin
	$(CURDIR)/tools/installbuilder/bin/builder build installer.xml windows --verbose --setvars project.outputDirectory=./$(RELEASE_DIR) project.version=$(VERSION) sourceDir=$(WINDOWS_DIR) project.shortName=bbnim
	$(CURDIR)/tools/installbuilder/bin/builder build installer.xml linux --verbose --setvars project.outputDirectory=./$(RELEASE_DIR) project.version=$(VERSION) sourceDir=$(LINUX_DIR) project.shortName=bbnim
	@echo "Compressing release packages..."
	cd $(RELEASE_DIR); \
	rm -f bbnim*.zip; \
	mv MANIFESTForWindows.md windows_amd64/MANIFESTF.md; \
	mv MANIFESTForLinux.md linux_amd64/MANIFESTF.md; \
	upx -9 windows_amd64/bb*.exe windows_amd64/get-machine-id.exe; \
	zip -r bbnim_windows_amd64_$(VERSION).zip *.txt *.pdf CHANGELOG.md README.md authentication.md windows_amd64; \
	zip -r bbnim_linux_amd64_$(VERSION).zip *.txt *.pdf CHANGELOG.md README.md authentication.md linux_amd64; \
	echo "Release zips created: bbnim_windows_amd64_$(VERSION).zip and bbnim_linux_amd64_$(VERSION).zip"

tag_release_prompt:
	@printf "Create git tag and push? (y/n) "; \
	read REPLY; \
	echo; \
	if [ "$$REPLY" = "y" ] || [ "$$REPLY" = "Y" ]; then \
		VERSION=$$(cat .version.tmp); \
		echo "Creating git tag $$VERSION and pushing..."; \
		git tag $$VERSION; \
		git push origin $$VERSION; \
		echo "Tag $$VERSION created and pushed."; \
	else \
		echo "Skipping git tag creation."; \
		VERSION=$$(cat .version.tmp) ; \
		echo "You can create the tag manually later with: git tag $$VERSION && git push origin $$VERSION"; \
		echo "Or create the tag on GitHub directly."; \
		rm -f .version.tmp; \
	fi


# --- Cleanup Targets ---
clean:
	@echo "Cleaning Go build artifacts..."
	make -C bbrootsvc clean
	make -C bbctl clean
	make -C bbnmssvc clean
	make -C bblogsvc clean
	make -C bbidpsvc clean
	# make -C frontend clean # Uncomment if frontend clean is desired here

clean_temporary_files:
	@echo "Cleaning temporary files from release process..."
	rm -rf *.zip *.tar.gz *.run *.json # Cleanup downloaded archives/configs
	rm -rf userguide # Remove cloned repos
	rm -rf $(CURDIR)/libs # Remove the libs directory, including mnms_installation
	rm -rf $(CURDIR)/tools # Remove the tools directory, including installbuilder
	rm -rf /tmp/caddy_linux_amd64 /tmp/caddy_windows_amd64 # Caddy temp extract
	rm -f .version.tmp # Remove temporary version file
	@echo "Temporary files cleaned."

deep_clean: clean
	@echo "Performing deep clean: removing all generated files and release folders..."
	rm -rf $(RELEASE_DIR)
	rm -rf caddy_2.6.3_linux_amd64.tar.gz* # Ensure all caddy downloads are gone
	rm -rf caddy_2.6.3_windows_amd64.zip*
	rm -rf npcap-1.72.exe* # If this was downloaded to root
	# Removed obsolete ../pcre2-10.43* cleanups as they are now managed implicitly via mnms_installation
	# Removed installbuilder.run cleanup as it's now managed within $(CURDIR)/tools/installbuilder
	@echo "Deep clean complete."

# --- Help Message ---
help:
	@echo "Usage: make <target>"
	@echo ""
	@echo "Main orchestration targets:"
	@echo "  release                 - Builds and packages the entire application for release (recommended)."
	@echo "                          - This target assumes system packages (mingw-w64, npm, etc.) are installed."
	@echo ""
	@echo "Environment Setup (run once per environment, may need 'sudo' for initial system packages):"
	@echo "  install_build_environment - Installs all necessary build tools and dependencies (InstallBuilder, mnms_installation)."
	@echo "  install_system_packages   - (Prerequisite, run separately with sudo) Installs system packages via apt-get and Go modules (requires sudo)."
	@echo ""
	@echo "Cleanup targets:"
	@echo "  clean                   - Removes Go build artifacts."
	@echo "  deep_clean              - Removes all generated files, including release folders, downloaded libraries, and temporary files."
	@echo ""
	@echo "Helper targets (usually called by 'release' or 'install_build_environment', but can be run individually for debugging/specific tasks):"
	@echo "  get_version             - Prompts for the release version."
	@echo "  setup_release_dirs      - Creates the release folder structure."
	@echo "  install_installbuilder  - Installs InstallBuilder (local to project, no sudo required)."
	@echo "  get_external_libs       - Clones and copies mnms_installation files (local to project, no sudo required)."
	@echo "  get_caddy_binaries      - Downloads Caddy binaries for Linux and Windows."
	@echo "  build_user_manual       - Generates the user manual PDF (installs mdpdf locally if needed)."
	@echo "  pack                    - Creates Windows and Linux installers using InstallBuilder."
	@echo "  tag_release_prompt      - Prompts to create and push a git tag."
	@echo "  clean_temporary_files   - Cleans temporary files generated during the release build."
	@echo ""
	@echo "Individual component builds:"
	@echo "  default                 - Builds frontend, bbrootsvc, bbctl, bblogsvc, bbnmssvc, bbidpsvc (all core Go components)."
	@echo "  devcmds                 - Builds backend Go components without re-building frontend."
	@echo "  dev                     - Smart build: runs 'default' if frontend 'dist' is missing, else 'devcmds'."
	@echo "  frontend                - Builds the web frontend."
	@echo "  bbrootsvc               - Builds the bbrootsvc."
	@echo "  bbctl                   - Builds the bbctl."
	@echo "  bblogsvc                - Builds the bblogsvc."
	@echo "  bbnmssvc                - Builds the bbnmssvc."
	@echo "  bbidpsvc                - Builds the bbidpsvc."
	@echo "  lint                    - Runs linting checks on Go code."
	@echo "  install_doc             - Generates API documentation and copies static documentation files."
	@echo "  release_build_bb        - Builds all main Go binaries for release (linux and windows)."
	@echo "  release_build_get-machine-id - Builds the get-machine-id tool for release."
