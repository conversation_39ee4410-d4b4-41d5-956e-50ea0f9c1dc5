import { Form, Input, Modal, InputNumber } from "antd";
import React from "react";

const DetectAnomaly = ({ open, onCreate, onCancel, loadingSave }) => {
  const [form] = Form.useForm();

  return (
    <Modal
      open={open}
      width={400}
      forceRender
      maskClosable={false}
      title={"Detect anomalies"}
      okText={"detect"}
      cancelText="CANCEL"
      confirmLoading={loadingSave}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            onCreate(values);
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form form={form} layout="vertical" name="form_in_modal_detect_log">
        <Form.Item
          label="Enter log"
          name="log"
          rules={[{ required: true, message: "input is required" }]}
        >
          <Input />
        </Form.Item>
        <Form.Item label="Enter score" name="score">
          <InputNumber
            style={{ width: "100%" }}
            min={0.0}
            max={1.0}
            step={0.05}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default DetectAnomaly;
