import React from "react";
import PropTypes from "prop-types";
import { Card, Typography, Space } from "antd";
import JsonView from "react18-json-view";
import "react18-json-view/src/style.css";
import ReactMarkdown from "react-markdown";

const { Title, Text } = Typography;

export function MarkdownText({ children, className, ...props }) {
  // Ensure children is always a string before passing to ReactMarkdown
  const stringContent = React.useMemo(() => {
    if (children === null || children === undefined) return "";
    if (typeof children === "string") return children;
    if (typeof children === "object") return JSON.stringify(children, null, 2);
    return String(children);
  }, [children]);

  return (
    <ReactMarkdown
      style={{
        fontSize: "0.875rem",
        lineHeight: "1.5rem",
        color: "#71717a", // zinc-500 equivalent
      }}
      className={className}
      {...props}
    >
      {stringContent}
    </ReactMarkdown>
  );
}

export const NodeOutputCard = ({ result }) => {
  const renderContent = (result) => {
    if (result?.format === "json") {
      return (
        <div style={{ overflowX: "auto" }}>
          <JsonView src={result.data} theme="monokai" />
        </div>
      );
    } else {
      // Convert data to string if it's an object
      const content =
        typeof result?.data === "object"
          ? JSON.stringify(result.data, null, 2)
          : result?.data || "";
      return <MarkdownText>{content}</MarkdownText>;
    }
  };

  return (
    <Card title={<Title level={4}>Result</Title>}>{renderContent(result)}</Card>
  );
};

NodeOutputCard.propTypes = {
  result: PropTypes.shape({
    format: PropTypes.oneOf(["json", "text"]),
    data: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  }),
};

const RunNodeResultCard = ({ value }) => {
  if (!value) {
    return <Text>Waiting result</Text>;
  }

  const formatDoc = (value) => {
    if (!value || !value.relevant_doc) return "";

    try {
      // Check if it's already an object or JSON string
      if (typeof value.relevant_doc === "object") {
        return JSON.stringify(value.relevant_doc, null, 2);
      }

      // Try to parse if it looks like JSON
      if (
        typeof value.relevant_doc === "string" &&
        (value.relevant_doc.startsWith("{") ||
          value.relevant_doc.startsWith("["))
      ) {
        return JSON.stringify(JSON.parse(value.relevant_doc), null, 2);
      }

      // Return as is if it's a string
      return value.relevant_doc;
    } catch (e) {
      // Fallback to string representation
      return String(value.relevant_doc || "");
    }
  };

  return (
    <Space direction="vertical" size="large" style={{ width: "100%" }}>
      <NodeOutputCard result={value.output} />
      <Card title={<Title level={4}>Relevant Document</Title>}>
        <MarkdownText>{formatDoc(value)}</MarkdownText>
      </Card>
    </Space>
  );
};

RunNodeResultCard.propTypes = {
  value: PropTypes.shape({
    input: PropTypes.string.isRequired,
    output: PropTypes.shape({
      type: PropTypes.oneOf(["json", "text"]).isRequired,
      data: PropTypes.oneOfType([PropTypes.string, PropTypes.object])
        .isRequired,
    }).isRequired,
    relevant_doc: PropTypes.string.isRequired,
  }).isRequired,
};

export default RunNodeResultCard;
