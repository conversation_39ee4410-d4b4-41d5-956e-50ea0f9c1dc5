import { SettingOutlined } from "@ant-design/icons";
import {
  App,
  Button,
  Flex,
  Input,
  InputNumber,
  Popover,
  Radio,
  Space,
} from "antd";
import React, { useState } from "react";
import {
  useAnomalySettingsMutation,
  useSettingRealtimeMutation,
} from "../../app/services/anomalyApi";

const fieldStyle = {
  marginBottom: 8,
  display: "block",
  width: 250,
};

const AnomalySettingsPop = ({ clientName, settingType, title }) => {
  const { notification } = App.useApp();
  const [handleAnomalySettings, { isLoading }] = useAnomalySettingsMutation();
  const [open, setOpen] = useState(false);
  const [inputText, setInputText] = useState("");
  const [modelValue, setModelValue] = useState("");
  const [intervalValue, setIntervalValue] = useState(5);
  const [autoDetectvalue, setAutoDetectvalue] = useState("");
  const [distanceValue, setDistanceValue] = useState(0.4);
  const [ollamaValues, setOllamaValues] = useState({
    host: "",
    model: "",
    port: 0,
  });

  const resetField = () => {
    setInputText("");
    setModelValue("");
    setIntervalValue(5);
    setAutoDetectvalue("");
    setDistanceValue(0.4);
    setOllamaValues({
      host: "",
      model: "",
      port: 0,
    });
  };

  const hide = () => {
    resetField();
    setOpen(false);
  };
  const handleOpenChange = (newOpen) => {
    resetField();
    setOpen(newOpen);
  };

  const handleOkClick = async () => {
    let command = "";
    switch (settingType) {
      case "api_key":
        command = `anomaly config llm open-ai ${inputText}`;
        break;
      case "model":
        command = `anomaly config llm connect ${modelValue}`;
        break;
      case "pull_interval":
        command = `anomaly config detect interval ${intervalValue}m`;
        break;
      case "auto_detect":
        command = `anomaly config detect ${autoDetectvalue}`;
        break;
      case "distance":
        command = `anomaly config detect distance ${distanceValue}`;
        break;
      case "ollama":
        command = `anomaly config llm ollama ${ollamaValues.host} ${ollamaValues.port} ${ollamaValues.model} `;
        break;
      default:
        break;
    }
    try {
      await handleAnomalySettings({
        svc: clientName,
        command,
      }).unwrap();
      notification.success({
        message: command + "\n" + "successfully setting command sent!",
      });
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    } finally {
      hide();
    }
  };

  const content = (
    <Flex vertical>
      {settingType === "api_key" && (
        <Input
          style={fieldStyle}
          onChange={(e) => setInputText(e.target.value)}
          value={inputText}
        ></Input>
      )}
      {settingType === "model" && (
        <Radio.Group
          onChange={(e) => setModelValue(e.target.value)}
          value={modelValue}
          style={fieldStyle}
        >
          <Radio value="open-ai">open-ai</Radio>
          <Radio value="ollama">ollama</Radio>
        </Radio.Group>
      )}
      {settingType === "pull_interval" && (
        <InputNumber
          min={1}
          value={intervalValue}
          onChange={(v) => setIntervalValue(v)}
          addonAfter="min"
          style={fieldStyle}
        />
      )}
      {settingType === "auto_detect" && (
        <Radio.Group
          onChange={(e) => setAutoDetectvalue(e.target.value)}
          value={autoDetectvalue}
          style={fieldStyle}
        >
          <Radio value="enable">enable</Radio>
          <Radio value="disable">disable</Radio>
        </Radio.Group>
      )}
      {settingType === "distance" && (
        <InputNumber
          value={distanceValue}
          onChange={(v) => setDistanceValue(v)}
          style={fieldStyle}
        />
      )}
      {settingType === "ollama" && (
        <>
          <Input
            placeholder="host"
            style={fieldStyle}
            onChange={(e) =>
              setOllamaValues((prev) => ({ ...prev, host: e.target.value }))
            }
            value={ollamaValues.host}
          ></Input>
          <Input
            placeholder="model"
            style={fieldStyle}
            onChange={(e) =>
              setOllamaValues((prev) => ({ ...prev, model: e.target.value }))
            }
            value={ollamaValues.model}
          ></Input>
          <InputNumber
            placeholder="port"
            onChange={(v) => setOllamaValues((prev) => ({ ...prev, port: v }))}
            value={ollamaValues.port}
            style={fieldStyle}
          />
        </>
      )}
      <Flex gap={10} justify="flex-end">
        <Button onClick={() => hide()}>cancel</Button>
        <Button onClick={() => handleOkClick()}>ok</Button>
      </Flex>
    </Flex>
  );

  return (
    <Popover
      placement="bottom"
      title={title}
      content={content}
      trigger="click"
      open={open}
      onOpenChange={handleOpenChange}
    >
      <SettingOutlined />
    </Popover>
  );
};

export default AnomalySettingsPop;
