package mnms

import (
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	snmplib "github.com/deejross/go-snmplib"
	"github.com/gosnmp/gosnmp"
	"github.com/qeof/q"
)

type SnmpOptions struct {
	Port      uint16             `json:"port"`      // default 161
	Community string             `json:"community"` // default "public"
	Version   gosnmp.SnmpVersion `json:"version"`   // default 2
	Timeout   time.Duration      `json:"timeout"`   // default 2 seconds
}

// return read-all-only community and read-write-all community
func extractCommunityNames(output string) (string, string, error) {
	lines := strings.Split(output, "\n")
	communityNamePattern := `(\w+)\s+(read-all-only|read-write-all)`
	regex, err := regexp.Compile(communityNamePattern)
	if err != nil {
		return "", "", err
	}
	var readOnly string
	var readWrite string
	for _, line := range lines {
		match := regex.FindStringSubmatch(line)
		if len(match) == 3 {
			communityName := match[1]
			accessRight := match[2]

			if accessRight == "read-all-only" {
				readOnly = communityName
			}
			if accessRight == "read-write-all" {
				readWrite = communityName
			}
		}
	}
	return readOnly, readWrite, nil
}

// GetSNMPCommunity return read-only community and read-write community
func GetSNMPCommunity(user, pass, devIP string) (string, string, error) {
	dev, err := FindDevWithIP(devIP)
	if err != nil {
		return "", "", err
	}
	if dev.ModelName == "" {
		err := fmt.Errorf("error: invalid device model")
		return "", "", err
	}
	if !CheckSwitchCliModel(dev.ModelName) {
		err := fmt.Errorf("error: switch cli not available")
		return "", "", err
	}

	var cmdinfo CmdInfo

	// https://github.com/bbtechhive/mnms/pull/1057
	if strings.Contains(dev.ModelName, "EHG65") ||
		strings.Contains(dev.ModelName, "EHG24") {
		err = SendSwitch(&cmdinfo, dev, user, pass, "show snmp-community", 3)
	} else {
		err = SendSwitch(&cmdinfo, dev, user, pass, "show snmp community", 1)
	}
	if err != nil {
		q.Q(err)
		return "", "", err
	}

	return extractCommunityNames(cmdinfo.Result)
}

/*
var trapTypeMessage = []string{
	"cold start",
	"warm start",
	"link down",
	"link up",
	"authentication failure",
	"egp neighbor loss",
	"enterprise specific",
}*/

// snmp scan, get, set

func SnmpScan() error {
	cidrs, err := IfnetCidrs()
	if err != nil {
		q.Q("error: cannot figure out CIDRs", err)
		return fmt.Errorf("error: cidr, %v", err)
	}

	for _, cidr := range cidrs {
		err = ScanCIDR(cidr)
		if err != nil {
			q.Q("error: can't scan cidr", cidr, err)
			continue
		}
	}
	return nil
}

func ScanCIDR(cidr string) error {
	ipaddrs, err := GetIpAddrs(cidr)
	if err != nil {
		q.Q("error: cannot get ip addresses from CIDR", cidr)
		return err
	}

	ipChan := make(chan string, 10)
	done := make(chan bool)

	go func() {
		for _, ip := range ipaddrs {
			ipChan <- ip.String()
		}

		done <- true
	}()

	var wg sync.WaitGroup

loop:
	for {
		select {
		case <-done:
			q.Q("done")
			break loop
		case ipaddr := <-ipChan:
			wg.Add(1)
			go func() {
				defer wg.Done()
				err = probeSnmp(ipaddr)
				if err != nil {
					q.Q("error: probe snmp", err)
				}
			}()
		}
	}

	wg.Wait()
	q.Q(numModelsFound)

	return nil
}

func probeSnmp(ipaddr string) error {
	var err error
	var community string
	devInfo, err := FindDevWithIP(ipaddr)
	community = QC.SnmpOptions.Community
	if err == nil {
		if len(devInfo.ReadCommunity) > 0 {
			community = devInfo.ReadCommunity
		}
	}
	params := &gosnmp.GoSNMP{
		Target:                  ipaddr,
		Port:                    QC.SnmpOptions.Port,
		Community:               community,
		Version:                 QC.SnmpOptions.Version,
		Timeout:                 QC.SnmpOptions.Timeout,
		UseUnconnectedUDPSocket: true,
	}

	err = params.Connect()
	if err != nil {
		q.Q("error: snmp connect", err)
		return err
	}
	defer params.Conn.Close()

	oids := []string{
		"*******.*******.0",
		"*******.*******.0",
		"*******.*******.0",
		"*******.*******.0",
		"*******.*******.0",
		"*******.*******.0",
		"*******.*******.0",
		"*******.*******.0",
	}

	// XXX these may not work on SE400, SE500, CWR5805, ...
	atopOidSuffixes := []string{
		".*******.3.1",
		".*******.4.1",
		".*******.5.1",
		".*******.6.1",
		".*******.7.1",
		".1.1.0",
		".1.2.0",
		".1.3.0",
		".1.4.0",
		".1.5.0",
		".1.6.0",
		".1.7.0",
		".1.8.0",
		".1.9.0",
		".1.10.0",
		".1.11.0",
		".1.12.0",
		".1.13.0",
		".1.14.0",
	}

	result, err := params.Get(oids)
	if err != nil {
		q.Q("error: snmp get", ipaddr, err)
		return err
	}

	var atopOids []string
	atopOidPrefix := ""

	atopOidPrefix = parseSnmpResults(result, atopOidPrefix)

	if atopOidPrefix != "" {
		for _, Suffix := range atopOidSuffixes {
			atopOids = append(atopOids, atopOidPrefix+Suffix)
		}

		result, err := params.Get(atopOids)
		if err != nil {
			q.Q("error: snmp get", ipaddr, err)
			return err
		}
		parseSnmpResults(result, atopOidPrefix)
	}
	return nil
}

var numModelsFound int

func parseSnmpResults(result *gosnmp.SnmpPacket, atopOidPrefix string) string {
	model := GwdModelInfo{}
	prefix := ""

	for i, variable := range result.Variables {
		oid := variable.Name[1:]
		q.Q(i, oid)

		switch variable.Type {
		case gosnmp.OctetString:
			if strings.HasPrefix(oid, "*******.4.1.3755.") {
				if strings.HasSuffix(oid, ".1.6.0") ||
					strings.HasSuffix(oid, "3755.*******.9.0") {
					vv := variable.Value.([]byte)
					model.MACAddress = fmt.Sprintf("%.2X-%.2X-%.2X-%.2X-%.2X-%.2X",
						vv[0], vv[1], vv[2], vv[3], vv[4], vv[5])
					q.Q(model.MACAddress)
				} else if strings.HasSuffix(oid, ".1.5.0") {
					k := string(variable.Value.([]byte))
					model.Kernel = CleanStr(k)
				} else if strings.HasSuffix(oid, ".1.4.0") {
					apStr := string(variable.Value.([]byte))
					model.Ap = CleanStr(apStr)
				} else if strings.HasSuffix(oid, ".1.10.0") {
					m := string(variable.Value.([]byte))
					model.Model = CleanStr(m)
				}
			} else {
				q.Q(variable.Value.([]byte))
			}
		case gosnmp.ObjectIdentifier:
			prefix = variable.Value.(string)
			q.Q(prefix)
			prefix = prefix[1:]
		case gosnmp.IPAddress:
			ipaddr := variable.Value.(string)
			q.Q(ipaddr)
			if strings.HasPrefix(oid, "*******.4.1.3755.") {
				if strings.HasSuffix(oid, ".*******.3.1") {
					model.IPAddress = ipaddr
				} else if strings.HasSuffix(oid, ".*******.4.1") {
					model.Netmask = ipaddr
				} else if strings.HasSuffix(oid, ".*******.5.1") {
					model.Gateway = ipaddr
				}
			}
		default:
			q.Q(gosnmp.ToBigInt(variable.Value))
		}
	}

	if atopOidPrefix != "" && model.Model != "" {
		if model.Hostname == "" {
			model.Hostname = "unknown"
		}
		model.ScannedBy = QC.Name
		InsertModel(model, "snmp")
		numModelsFound++
		q.Q(numModelsFound, model)
	}

	return prefix
}

const SystemObjectID = ".*******.*******.0"

// SnmpGetObjectID get snmp object id from device
func SnmpGetObjectID(path string) (string, error) {
	rets, err := SnmpGet(path, []string{SystemObjectID})
	if err != nil {
		return "", err
	}
	if len(rets.Variables) <= 0 {
		return "", fmt.Errorf("not found")
	}
	return PDUToString(rets.Variables[0]), nil
}

// SnmpGet - get snmp data
func SnmpGet(address string, oids []string) (result *gosnmp.SnmpPacket, err error) {
	var community string
	devInfo, err := FindDevWithIP(address)
	community = QC.SnmpOptions.Community
	if err == nil {
		if len(devInfo.ReadCommunity) > 0 {
			community = devInfo.ReadCommunity
		}
	}
	params := &gosnmp.GoSNMP{
		Target:                  address,
		Port:                    QC.SnmpOptions.Port,
		Community:               community,
		Version:                 QC.SnmpOptions.Version,
		Timeout:                 QC.SnmpOptions.Timeout,
		UseUnconnectedUDPSocket: true,
	}

	q.Q("snmp get", params)
	err = params.Connect()
	if err != nil {
		q.Q("error: snmp connect", err)
		// cmdinfo.Status = "error: cannot contact snmp target"
		return nil, err
	}
	defer params.Conn.Close()
	result, err = params.Get(oids)
	if err != nil {
		q.Q("error: snmp get", err)
		return nil, err
	}
	return result, nil
}

// SnmpWalk - walk snmp data
func SnmpWalk(address string, oid string) (result []gosnmp.SnmpPDU, err error) {
	var community string
	devInfo, err := FindDevWithIP(address)
	community = QC.SnmpOptions.Community
	if err == nil {
		if len(devInfo.ReadCommunity) > 0 {
			community = devInfo.ReadCommunity
		}
	}
	params := &gosnmp.GoSNMP{
		Target:                  address,
		Port:                    QC.SnmpOptions.Port,
		Community:               community,
		Version:                 QC.SnmpOptions.Version,
		Timeout:                 QC.SnmpOptions.Timeout,
		UseUnconnectedUDPSocket: true,
	}

	err = params.Connect()
	if err != nil {
		q.Q("error: snmp connect", err)
		// cmdinfo.Status = "error: cannot contact snmp target"
		return nil, err
	}
	defer params.Conn.Close()
	result, err = params.WalkAll(oid)
	if err != nil && len(result) == 0 {
		q.Q("error: snmp walk", err)
		return nil, err
	}
	return result, nil
}

// SnmpBulk - Bulk snmp data
func SnmpBulk(address string, oid string) (result []gosnmp.SnmpPDU, err error) {
	var community string
	devInfo, err := FindDevWithIP(address)
	community = QC.SnmpOptions.Community
	if err == nil {
		if len(devInfo.ReadCommunity) > 0 {
			community = devInfo.ReadCommunity
		}
	}
	params := &gosnmp.GoSNMP{
		Target:                  address,
		Port:                    QC.SnmpOptions.Port,
		Community:               community,
		Version:                 QC.SnmpOptions.Version,
		Timeout:                 QC.SnmpOptions.Timeout,
		UseUnconnectedUDPSocket: true,
	}

	err = params.Connect()
	if err != nil {
		q.Q("error: snmp connect", err)
		// cmdinfo.Status = "error: cannot contact snmp target"
		return nil, err
	}
	defer params.Conn.Close()
	result, err = params.BulkWalkAll(oid)
	if err != nil && len(result) == 0 {
		q.Q("error: snmp bulk", err)
		return nil, err
	}
	return result, nil
}

// SnmpSet - set snmp data
func SnmpSet(address, oid string, value string, valuetype string) (result *gosnmp.SnmpPacket, err error) {
	var community string
	devInfo, err := FindDevWithIP(address)
	community = QC.SnmpOptions.Community
	if err == nil {
		if len(devInfo.WriteCommunity) > 0 {
			community = devInfo.WriteCommunity
		}
	}
	params := &gosnmp.GoSNMP{
		Target:                  address,
		Port:                    QC.SnmpOptions.Port,
		Community:               community,
		Version:                 QC.SnmpOptions.Version,
		Timeout:                 QC.SnmpOptions.Timeout,
		UseUnconnectedUDPSocket: true,
	}

	t := GetType(valuetype)
	q.Q("snmp set type", t, valuetype)
	anyVal := ConvertSetValue(value, valuetype)
	if anyVal == nil {
		return nil, fmt.Errorf("error: value type not supported (%s)", valuetype)
	}
	data := []gosnmp.SnmpPDU{{Name: oid, Value: anyVal, Type: gosnmp.Asn1BER(t)}}
	q.Q(data)
	err = params.Connect()
	if err != nil {
		q.Q("error: snmp connect", err)
		// cmdinfo.Status = "error: cannot contact snmp target"
		return nil, err
	}
	defer params.Conn.Close()
	return params.Set(data)
}

// PDUToString - convert PDU to string
func PDUToString(pdu gosnmp.SnmpPDU) string {
	var resStr string
	switch pdu.Type {
	case gosnmp.OctetString:
		value := pdu.Value.([]byte)
		// check for hex string
		// if true then hex string or else normal string
		if strings.Contains(strconv.Quote(string(value)), "\\x") {
			addr := net.HardwareAddr(value[:])
			r, err := net.ParseMAC(addr.String())
			if err != nil {
				resStr = hex.EncodeToString(value)
			} else {
				resStr = strings.ToUpper(r.String())
			}
		} else { // normal string
			resStr = string(value)
		}
	case gosnmp.ObjectIdentifier:
		resStr = pdu.Value.(string)
	case gosnmp.IPAddress:
		resStr = pdu.Value.(string)

	default:
		resStr = fmt.Sprintf("%d", (gosnmp.ToBigInt(pdu.Value)))
	}
	resStr = CleanStr(resStr)
	return resStr
}

// Enable SNMP of target device.
// High level command
//
// Usage : snmp enable [mac address]
//
//		   snmp disable [mac address]
//
//	[mac address] : target device mac address
//
// Example :
//
//		snmp enable AA-BB-CC-DD-EE-FF
//
//	 snmp disable AA-BB-CC-DD-EE-FF
//
// Use snmp get/set/communities/update.
//
// Usage : snmp get [ip address] [oid]
//
//	[ip address]  : target device ip address
//	[oid]         : target oid
//
// Example : snmp get ********* *******.*******.0
//
// Usage : snmp set [ip address] [oid] [value] [value type]
//
//	[ip address]  : target device ip address
//	[oid]         : target oid
//	[value]       : would be set value
//	[value type]  : would be set value type.(OctetString, BitString, SnmpNullVar, Counter,
//	                Counter64, Gauge, Opaque, Integer, ObjectIdentifier, IpAddress, TimeTicks)
//
// Example : snmp set ********* *******.*******.0 www.atop.com.tw OctetString
//
// Usage: snmp communities [mac]
// Read device's SNMP communities and update to system.
//
//	[mac]      : Device mac address
//
// Example: snmp communities 00-60-E9-27-E3-39
//
// Usage: snmp update community [mac] [read community] [write community]
// Update device's SNMP communities manually.
//
//	[mac]            : Device mac address
//	[read community] : Device snmp read community
//	[write community]: Device snmp write community
//
// Example: snmp update community 00-60-E9-27-E3-39 public private
//
// Usage: snmp options [port] [community] [version] [timeout]
// Update global snmp options.
//
//	[port]     : snmp listen port
//	[community]: snmp community
//	[version]  : snmp version
//	[timeout]  : snmp timeout
//
// Example: snmp options 161 public 2c 2
func SnmpCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	ws := strings.Split(cmd, " ")

	// snmp config syslog set [MAC Address] [Enable] [Server IP] [Server Port] [Log Level] [Log to Flash]
	if ws[1] == "config" && ws[2] == "syslog" && ws[3] == "set" {
		if len(ws) != 10 {
			cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 10 but got %d", len(ws))
			return cmdinfo
		}
		return SnmpConfigSyslogSet("syslog", cmdinfo)
	}
	// snmp config syslog get [MAC Address]
	if ws[1] == "config" && ws[2] == "syslog" && ws[3] == "get" {
		if len(ws) != 5 {
			cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 5 but got %d", len(ws))
			return cmdinfo
		}
		return SnmpConfigSyslogGet(cmdinfo)
	}
	// snmp options {port} {community} {version} {timeout}
	// snmp options 161 private 2c 2
	if ws[1] == "options" {
		if len(ws) != 6 {
			cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 6 but got %d", len(ws))
			return cmdinfo
		}
		q.Q(ws)
		port, err := strconv.Atoi(ws[2])
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: port %v", err)
			return cmdinfo
		}
		timeout, err := strconv.Atoi(ws[5])
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: timeout %v", err)
			return cmdinfo
		}
		var version gosnmp.SnmpVersion
		switch ws[4] {
		case "1":
			version = gosnmp.Version1
		case "2c":
			version = gosnmp.Version2c
		case "3":
			version = gosnmp.Version3
		default:
			cmdinfo.Status = fmt.Sprintf("error: version %v, accept 1|2c|3", err)
			return cmdinfo
		}
		q.Q(port, ws[3], version, timeout)

		QC.SnmpOptions = SnmpOptions{
			Port:      uint16(port),
			Community: ws[3],
			Version:   version,
			Timeout:   time.Duration(timeout) * time.Second,
		}
		cmdinfo.Status = "ok"
		q.Q(QC.SnmpOptions)
		return cmdinfo
	}

	if ws[1] == "communities" {
		// read communities and write to DevInfo
		// snmp communities [mac]
		if len(ws) != 3 {
			cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 3 but got %d", len(ws))
			return cmdinfo
		}
		standardMac := cmdinfo.DevId
		dev, err := FindDev(standardMac)
		if err != nil {
			cmdinfo.Status = "error: device not found"
			return cmdinfo
		}
		// Get the username and password
		username, password := GetCredentials(dev)
		q.Q(standardMac, username, password)
		// get communities
		r, rw, err := GetSNMPCommunity(username, password, dev.IPAddress)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %v", err)
			return cmdinfo
		}
		err = InsertCommunities(dev.Mac, r, rw)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %v", err)
			return cmdinfo
		}
		cmdinfo.Status = "ok"

		return cmdinfo
	}
	// snmp update community {mac} {read community} {write community}
	if ws[1] == "update" && ws[2] == "community" {
		r := ws[4]
		w := ws[5]
		standardMac := cmdinfo.DevId
		if len(ws) != 6 {
			cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 6 but got %d", len(ws))
			return cmdinfo
		}
		dev, err := FindDev(standardMac)
		if err != nil {
			cmdinfo.Status = "error: device not found"
			return cmdinfo
		}
		err = InsertCommunities(dev.Mac, r, w)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %v", err)
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		return cmdinfo
	}
	// snmp get {ip_adddress} {oid}
	if ws[1] == "get" {
		if len(ws) != 4 {
			cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 4 but got %d", len(ws))
			return cmdinfo
		}
		address := ws[2]
		oid := ws[3]
		oids := []string{oid}
		res, err := SnmpGet(address, oids)
		// res, err := params.Get(oids)
		if err != nil {
			q.Q(err)
			cmdinfo.Status = fmt.Sprintf("error: %v", err)
			return cmdinfo
		}
		var resStr string
		q.Q("snmp get ", res.Variables)

		for _, variable := range res.Variables {
			oid := variable.Name[1:]
			resStr = PDUToString(variable)
			q.Q(oid, resStr)
		}
		cmdinfo.Status = "ok"
		cmdinfo.Result = resStr
		return cmdinfo
	}
	// snmp walk {ip_adddress} {oid}
	if ws[1] == "walk" {
		if len(ws) != 4 {
			cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 4 but got %d", len(ws))
			return cmdinfo
		}
		address := ws[2]
		oid := ws[3]
		cmdinfo.Status = "ok"
		go func(cmdinfo CmdInfo) {
			type walkResult struct {
				Oid   string
				Value string
			}
			res, err := SnmpWalk(address, oid)
			if err != nil {
				q.Q(err)
				SendSyslog(LOG_INFO, "RunCmd", fmt.Sprintf("error: %v", err))
				return
			}
			q.Q("snmp walk ", res)
			walks := []walkResult{}
			for _, variable := range res {
				oid := variable.Name[1:]
				resStr := PDUToString(variable)
				walks = append(walks, walkResult{Oid: oid, Value: resStr})
			}
			b, err := json.Marshal(&walks)
			if err != nil {
				q.Q(err)
				SendSyslog(LOG_INFO, "RunCmd", fmt.Sprintf("error: %v", err))
				return
			}
			SendSyslog(LOG_INFO, "RunCmd", string(b))
		}(*cmdinfo)
		return cmdinfo
	}
	// snmp bulk {ip_adddress} {oid}
	if ws[1] == "bulk" {
		if len(ws) != 4 {
			cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 4 but got %d", len(ws))
			return cmdinfo
		}
		address := ws[2]
		oid := ws[3]
		cmdinfo.Status = "ok"
		go func(cmdinfo CmdInfo) {
			type walkResult struct {
				Oid   string
				Value string
			}
			res, err := SnmpBulk(address, oid)
			if err != nil {
				q.Q(err)
				SendSyslog(LOG_INFO, "RunCmd", fmt.Sprintf("error: %v", err))
				return
			}
			q.Q("snmp bulk ", res)
			walks := []walkResult{}
			for _, variable := range res {
				oid := variable.Name[1:]
				resStr := PDUToString(variable)
				walks = append(walks, walkResult{Oid: oid, Value: resStr})
			}
			b, err := json.Marshal(&walks)
			if err != nil {
				q.Q(err)
				SendSyslog(LOG_INFO, "RunCmd", fmt.Sprintf("error: %v", err))
				return
			}
			SendSyslog(LOG_INFO, "RunCmd", string(b))
		}(*cmdinfo)
		return cmdinfo
	}

	// snmp set {ip_adddress} {oid} {value} {type}
	if ws[1] != "set" {
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	}
	address := ws[2]
	oid := ws[3]
	if len(ws) != 6 {
		cmdinfo.Status = fmt.Sprintf("error: invalid command arguments, expected 6 but got %d", len(ws))
		return cmdinfo
	}
	value := ws[4]
	valuetype := ws[5]

	pkt, err := SnmpSet(address, oid, value, valuetype)
	if err != nil {
		q.Q(err)
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}

	if uint8(pkt.Error) > 0 {
		q.Q(pkt.Error)
		cmdinfo.Status = fmt.Sprintf("error: %v", pkt.Error.String())
		return cmdinfo
	}
	q.Q(pkt.Variables)
	cmdinfo.Status = "ok"
	return cmdinfo
}

func ConvertSetValue(val, t string) any {
	t = strings.TrimSpace(t)
	switch t {
	case "OctetString":
		return val
	case "Integer":
		// val convert to int
		v, err := strconv.Atoi(val)
		if err != nil {
			q.Q("error: convert to int", err)
			return nil
		}
		return v
	default:
		return val
	}
}

func GetType(t string) byte {
	t = strings.TrimSpace(t)
	switch t {
	case "OctetString":
		return byte(gosnmp.OctetString)
	case "BitString":
		return byte(gosnmp.BitString)
	case "SnmpNullVar":
		return byte(gosnmp.Null)
	case "Counter":
		return byte(gosnmp.Counter32)
	case "Counter64":
		return byte(gosnmp.Counter64)
	case "Gauge":
		return byte(gosnmp.Gauge32)
	case "Opaque":
		return byte(gosnmp.Opaque)
	case "Integer":
		return byte(gosnmp.Integer)
	case "ObjectIdentifier":
		return byte(gosnmp.ObjectIdentifier)
	case "IpAddress":
		return byte(gosnmp.IPAddress)
	case "TimeTicks":
		return byte(gosnmp.TimeTicks)
	}

	return byte(0)
}

type snmpHandler struct{}

func (h snmpHandler) OnError(addr net.Addr, err error) {
	q.Q(addr.String(), err)
}

func (h snmpHandler) OnTrap(addr net.Addr, trap snmplib.Trap) {
	prettyPrint, _ := json.Marshal(trap)
	if QC.IsRoot {
		// TODO save data to to q
		q.Q("trapserver :", string(prettyPrint))
	} else {
		q.Q("trapserver :", string(prettyPrint))
		trapVersion := trap.Version
		snmpTrapOID := map[string]string{
			".*******.*******.5.1": "coldStart",
			".*******.*******.5.2": "warmStart",
			".*******.*******.5.3": "linkDown",
			".*******.*******.5.4": "linkUp",
			".*******.*******.5.5": "authenticationFailure",
			".*******.*******.5.6": "egpNeighborLoss",
		}
		snmpTrapType := map[int]string{
			0: "coldStart",
			1: "warmStart",
			2: "linkDown",
			3: "linkUp",
			4: "authenticationFailure",
			5: "egpNeighborLoss",
		}
		TrapType := ""
		portInfo := ""
		// .*******.*******.4.1.0 : trap type
		// .*******.*******.1.1   : port

		if trapVersion == 1 {
			tmpTrapType := trap.TrapType
			if tmpTrapType > 5 {
				q.Q("error: trap format error")
				return
			}
			TrapType = snmpTrapType[tmpTrapType]
			if TrapType == "linkDown" || TrapType == "linkUp" {
				snmpTrapPort, ok := trap.Other.(int)
				q.Q(snmpTrapPort, trap.Other)
				if !ok {
					q.Q("error: trap port format error")
					return
				}
				portInfo = "Port " + strconv.Itoa(snmpTrapPort)
			}
		} else if trapVersion == 2 {
			snmpTrapOIDSnmplibOid, ok := trap.VarBinds[".*******.*******.4.1.0"].(snmplib.Oid)
			if !ok {
				q.Q("error: trap format error")
				return
			}
			SnmpTrapOIDStr := snmpTrapOIDSnmplibOid.String()
			if len(SnmpTrapOIDStr) == 0 || SnmpTrapOIDStr == "" {
				q.Q("error: SnmpTrapOID empty")
				return
			}
			TrapType, ok = snmpTrapOID[SnmpTrapOIDStr]
			if !ok {
				q.Q("error: unknow trap type")
				return
			}
			if TrapType == "linkDown" || TrapType == "linkUp" {
				snmpTrapPort, ok := trap.VarBinds[".*******.*******.1.1"].(int)
				// q.Q(snmpTrapPort, trap.VarBinds[".*******.*******.1.1"])
				if !ok {
					q.Q("error: trap port format error")
					return
				}
				portInfo = "Port " + strconv.Itoa(snmpTrapPort)
			}
		}

		noPortAddress := strings.Split(trap.Address, ":")
		trapSyslogString := noPortAddress[0] + " " + portInfo + " " + TrapType
		// q.Q(trapSyslogString)
		err := SendSyslog(LOG_NOTICE, "trapserver", trapSyslogString)
		if err != nil {
			q.Q("error: sending trap syslog", err)
		}
	}
}

func StartTrapServer() {
	ws := strings.Split(QC.TrapServerAddr, ":")
	if len(ws) < 2 {
		q.Q("Invalid trap server address", QC.TrapServerAddr)
		return
	}
	addr := ws[0]
	port, err := strconv.Atoi(ws[1])
	if err != nil {
		q.Q(err)
		return
	}
	q.Q(addr, port)
	server, err := snmplib.NewTrapServer(addr, port)
	if err != nil {
		q.Q(err)
		return
	}
	server.ListenAndServe(snmpHandler{})
}

// Use snmp to configure syslog setting.
//
// Usage :snmp config syslog set [mac address] [status] [server ip] [server port] [server level] [log to flash]
//
//	[mac address] : target device mac address
//	[status]      : use snmp to configure syslog enable/disable
//	[server ip]   : use snmp to configure server ip address
//	[server port] : use snmp to configure server port
//	[server level]: use snmp to configure server log level
//	[log to flash]: use snmp to configure log to flash
//
// Example :
//
// snmp config syslog set AA-BB-CC-DD-EE-FF 1 ********* 5514 1 1
func SnmpConfigSyslogSet(cate string, cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	standardMac := cmdinfo.DevId
	status := ws[5]
	serverIp := ws[6]
	serverPort := ws[7]
	serverLevel := ws[8]
	logToFlash := ws[9]
	dev, err := FindDev(standardMac)
	if err != nil {
		cmdinfo.Status = "error: device not found"
		return cmdinfo
	}

	// status setting
	err = SetSnmpOneCommand(dev.IPAddress, cate, status, "status")
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: snmp set status %v, %v", err, ErrorSnmpfunction)
		return cmdinfo
	}
	// server ip setting
	err = SetSnmpOneCommand(dev.IPAddress, cate, serverIp, "server-ip")
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v, %v", err, ErrorSnmpfunction)
		return cmdinfo
	}
	// server port setting
	err = SetSnmpOneCommand(dev.IPAddress, cate, serverPort, "server-port")
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v, %v", err, ErrorSnmpfunction)
		return cmdinfo
	}
	// server lavel setting
	err = SetSnmpOneCommand(dev.IPAddress, cate, serverLevel, "server-level")
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v, %v", err, ErrorSnmpfunction)
		return cmdinfo
	}
	// log to flash setting
	err = SetSnmpOneCommand(dev.IPAddress, cate, logToFlash, "LogToFlash")
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: snmp set logtoflash %v, %v", err, ErrorSnmpfunction)
		return cmdinfo
	}
	cmdinfo.Status = "ok"
	return cmdinfo
}

// Use snmp to get syslog setting.
//
// Usage : snmp config syslog get [mac address]
//
//	[mac address] : target device mac address
//
// Example :
//
//	snmp config syslog get 00-60-E9-18-3C-3C
func SnmpConfigSyslogGet(cmdinfo *CmdInfo) *CmdInfo {
	standardMac := cmdinfo.DevId
	dev, err := FindDev(standardMac)
	if err != nil {
		cmdinfo.Status = "error: device not found"
		return cmdinfo
	}
	v, err := GetSnmpSyslogStatus(dev.IPAddress)
	if err != nil {
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	b, err := json.Marshal(v)
	if err != nil {
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	cmdinfo.Result = string(b)
	cmdinfo.Status = "ok"
	return cmdinfo
}
