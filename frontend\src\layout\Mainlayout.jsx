import { PageContainer, ProLayout } from "@ant-design/pro-components";
import _DefaultProps from "./_DefaultProps";
import useWebSocket from "react-use-websocket";
import { useEffect, useState } from "react";
import { useTheme, useThemeMode } from "antd-style";
import { Link, Outlet, useLocation, useNavigate } from "react-router-dom";
import { App, Dropdown, Spin } from "antd";
import {
  CustomerServiceOutlined,
  LogoutOutlined,
  MacCommandOutlined,
  WechatWorkOutlined,
} from "@ant-design/icons";
import atopLogo from "../assets/images/NIMBL_Logo.svg";
import atopDarklogo from "../assets/images/darkmode-logo.svg";
import ThemeController from "../utils/themes/ThemeController";
import SettingsComp from "../components/comman/SettingsComp";
import { useDispatch, useSelector } from "react-redux";
import { logoutUser } from "../features/auth/userAuthSlice";
import { useThemeStore } from "../utils/themes/useStore";
import { eventLogSelector } from "../features/eventLog/eventLogSlice";
import {
  extractSocketResult,
  socketControlSelector,
} from "../features/socketControl/socketControlSlice";
import SyslogSettingDrawer from "../components/drawer/SyslogSettingDrawer";
import TrapSettingDrawer from "../components/drawer/TrapSettingDrawer";
import FirmwareDrawer from "../components/drawer/FirmwareDrawer";
import SaveRuunningConfigDrawer from "../components/drawer/SaveRuunningConfigDrawer";
import NetworkSettingDrawer from "../components/drawer/NetworkSettingDrawer";

import LicenseAlert from "../components/comman/LicenseAlert";
import ServerStatus from "../components/comman/ServerStatus";
import { ErrorBoundries } from "./FallbackErrorBoundry";
import { licenseAlertSelector } from "../features/socketControl/licenseAlertSlice";
import defaultAvatar from "../assets/images/defaultAvatar.webp";
import { RootClusterInfo } from "../features/clusterInfo/clusterInfoSlice";
import {
  layoutSliceSelector,
  setIsCollapsed,
} from "../features/comman/layoutSlice";
import { FloatButton } from "antd/lib";
import CommandModal from "../components/comman/CommandModal";
import {
  selectAssistantFlow,
  showChatSession,
} from "../features/aiassist/aiassistSlice";
import ChatSession from "../components/aiassistant/ChatSession";

const MainLayout = () => {
  const { mode, wsURL } = useThemeStore();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [openCmdModal, setOpenCmdModal] = useState(false);
  const token = useTheme();
  const { appearance } = useThemeMode();
  let location = useLocation();
  const [pathname, setPathname] = useState(location.pathname);
  const { notification } = App.useApp();
  const { firmwareNotification } = useSelector(eventLogSelector);
  const { socketLoading } = useSelector(socketControlSelector);
  const { isCollapsed } = useSelector(layoutSliceSelector);
  const { showLicenseWaterMark, featureEnabled } =
    useSelector(licenseAlertSelector);
  const assistantFlow = useSelector(selectAssistantFlow);

  const { lastMessage } = useWebSocket(`${wsURL}/api/v1/ws`, {
    onOpen: () => {
      console.log("Socket connection established.");
    },
    shouldReconnect: (closeEvent) => true,
  });

  useEffect(() => {
    setPathname(location.pathname || "/");
  }, [location]);

  useEffect(() => {
    if (firmwareNotification !== "") {
      const splitMsg = firmwareNotification.split("firmware:");
      notification.info({
        message: `Firmware progress`,
        description: splitMsg[1],
        placement: "topRight",
      });
    }
  }, [firmwareNotification]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (lastMessage !== null) {
      requestAnimationFrame(() => {
        dispatch(extractSocketResult(lastMessage.data));
      });
    }
  }, [lastMessage]);

  const handleMenuClick = (e) => {
    if (e.key === "logout") {
      sessionStorage.removeItem("nmstoken");
      sessionStorage.removeItem("nmsuser");
      sessionStorage.removeItem("nmsuserrole");
      sessionStorage.removeItem("prevTopologyNodesData");
      sessionStorage.removeItem("qrcodeurl");
      sessionStorage.removeItem("sessionid");
      sessionStorage.removeItem("is2faenabled");
      dispatch(logoutUser());
      navigate("/login");
    }
  };
  const handleCollapseChange = (collapsed) => {
    dispatch(setIsCollapsed(collapsed));
  };
  useEffect(() => {
    dispatch(setIsCollapsed(true));
    // load root information on mount first time
    dispatch(RootClusterInfo({}));
  }, []);

  const loggedinUser = sessionStorage.getItem("nmsuser")
    ? sessionStorage.getItem("nmsuser")
    : "admin";

  return (
    <Spin tip="Loading" size="small" spinning={socketLoading}>
      <ProLayout
        collapsed={isCollapsed}
        onCollapse={handleCollapseChange}
        {..._DefaultProps({
          anomalies: featureEnabled.includes("anomalies"),
          idps: featureEnabled.includes("idps"),
        })}
        waterMarkProps={
          showLicenseWaterMark
            ? {
                content: "NIMBL Missing Valid License !",
              }
            : undefined
        }
        siderWidth={220}
        layout="mix"
        fixSiderbar
        fixedHeader
        hasSiderMenu={true}
        siderMenuType="sub"
        ErrorBoundary={ErrorBoundries}
        menu={{
          collapsedShowGroupTitle: false,
        }}
        location={{
          pathname,
        }}
        logo={
          <img
            src={appearance === "dark" ? atopDarklogo : atopLogo}
            alt="BlackBear TechHive"
            style={{ height: "50px" }}
          />
        }
        title="BlackBear TechHive"
        headerTitleRender={(logo) => (
          <a
            target="_blank"
            href="https://blackbeartechhive.com"
            rel="noreferrer"
          >
            {logo}
          </a>
        )}
        avatarProps={{
          src: defaultAvatar,
          size: "default",
          title: loggedinUser,
          alt: "Avatar",
          render: (props, dom) => {
            return (
              <Dropdown
                trigger={["click"]}
                placement="bottom"
                arrow
                menu={{
                  items: [
                    {
                      key: "logout",
                      icon: <LogoutOutlined />,
                      label: "Logout",
                    },
                  ],
                  onClick: handleMenuClick,
                }}
              >
                {dom}
              </Dropdown>
            );
          },
        }}
        actionsRender={(props) => [
          <ServerStatus isDashboardPage={true} />,
          process.env.NODE_ENV === "development" && <SettingsComp />,
          <ThemeController />,
        ]}
        menuItemRender={(item, dom) => <Link to={item.path || "/"}>{dom}</Link>}
        token={{
          sider: {
            colorBgMenuItemCollapsedElevated: token.colorBgContainer,
            colorMenuBackground: token.colorBgContainer,
            colorBgMenuItemSelected:
              mode === "dark" ? token.colorPrimary : token.colorPrimaryBg,
            colorTextMenuSelected:
              mode === "dark" ? token.colorText : token.colorPrimary,
          },
          pageContainer: {
            paddingBlockPageContainerContent: 0,
            paddingInlinePageContainerContent: 16,
          },
        }}
      >
        <PageContainer
          header={{
            title: "",
            breadcrumb: [],
          }}
        >
          <div style={{ paddingBlock: "16px" }}>
            <Outlet />
          </div>

          <FloatButton.Group
            shape="circle"
            style={{
              insetInlineEnd: 10,
              insetBlockEnd: 24,
            }}
          >
            <FloatButton
              tooltip="NIMBL Assistant"
              type="primary"
              icon={<WechatWorkOutlined />}
              onClick={() => dispatch(showChatSession())}
            />
            <FloatButton
              tooltip="commands"
              type="primary"
              icon={<MacCommandOutlined />}
              onClick={() => setOpenCmdModal(true)}
            />
          </FloatButton.Group>
          <CommandModal
            open={openCmdModal}
            onCancel={() => setOpenCmdModal(false)}
          />
          <ChatSession assistantFlow={assistantFlow} />
          <NetworkSettingDrawer />

          <SyslogSettingDrawer />
          <TrapSettingDrawer />
          <FirmwareDrawer />
          <SaveRuunningConfigDrawer />
          <LicenseAlert />
        </PageContainer>
      </ProLayout>
    </Spin>
  );
};

export default MainLayout;
