// eslint-disable-next-line no-unused-vars
import React, { useState, useEffect } from "react";
import RcResizeObserver from "rc-resize-observer";
import {
  ProCard,
  ProDescriptions,
  StatisticCard,
} from "@ant-design/pro-components";
import Log<PERSON><PERSON><PERSON><PERSON> from "./LogPieChart";
import LogHistory from "./LogHistory";
import {
  App,
  Button,
  Flex,
  Form,
  Input,
  List,
  Modal,
  Radio,
  Switch,
  Tag,
  Typography,
  theme,
} from "antd";

import {
  useAddAnomalyMutation,
  useAnomalyBackupMutation,
  useGetAnomalyReportsbyidQuery,
  useImportFileMutation,
  useGetRealtimeReportQuery,
  useGetRealtimeReportWithTypeQuery,
  useClearRealtimeReportMutation,
} from "../../app/services/anomalyApi";
import AnomalySettingsPop from "./AnomalySettingsPop";
import AnomalyRealtimeStats from "./RealtimeStats";

const { Statistic } = StatisticCard;

const options = [
  {
    label: "All",
    value: "all",
  },
  {
    label: "Normal",
    value: "normal",
  },
  {
    label: "Anomaly",
    value: "anomaly",
  },
  {
    label: "Error",
    value: "error",
  },
];

// eslint-disable-next-line react/prop-types
const AnomalySettingStats = ({ clientName, settings = {} }) => {
  const token = theme.useToken().token;
  const [openAnomModel, setOpenAnomModel] = useState(false);
  const [openAddModels, setOpenAddModels] = useState(false);
  const [modalType, setModalType] = useState("detect");
  const [typeSelect, setTypeSelect] = useState("all");
  const [responsive, setResponsive] = useState(false);
  const [stats, setStats] = useState({
    anomaly: 0,
    error: 0,
    normal: 0,
  });
  const [selectedId, setSelectedId] = useState("");
  const [page, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(5);
  // state to be used to indicate whether to display realtime information
  const [displayRealtime, setDisplayRealtime] = useState(false);
  const { data: realtimeInfo, refetch: refetchRealtimeInfo } =
    useGetRealtimeReportQuery(clientName, {
      skip: !displayRealtime,
    });
  const [data, setData] = useState({
    total: 0,
    messages: [],
  });
  const { data: realtimeMessages, refetch: refetchRealtimeMessages } =
    useGetRealtimeReportWithTypeQuery(
      {
        id: clientName,
        type: typeSelect,
        page,
        pageSize,
      },
      {
        skip: !displayRealtime,
      }
    );
  const [clearRealtimeReport] = useClearRealtimeReportMutation();

  const handleClearRealtimeReport = async () => {
    try {
      console.log("clearing realtime report", clientName);
      const response = await clearRealtimeReport({
        client: clientName,
      }).unwrap();
      console.log("response", response);

      // refetch realtime report after clearing
      refetchRealtimeInfo();
      refetchRealtimeMessages();
    } catch (error) {
      console.error("Failed to clear realtime report", error);
    }
  };

  useEffect(() => {
    let intervalId;
    if (displayRealtime) {
      intervalId = setInterval(() => {
        refetchRealtimeInfo();
        refetchRealtimeMessages();
      }, 60000); // Poll every minute
    } else {
      clearInterval(intervalId);
    }
    return () => clearInterval(intervalId);
  }, [displayRealtime, refetchRealtimeInfo, refetchRealtimeMessages]);

  useEffect(() => {
    console.log("fetch realtime info", realtimeInfo);
    if (realtimeInfo) {
      setStats({
        anomaly: realtimeInfo.total_anomaly,
        error: realtimeInfo.total_error,
        normal:
          realtimeInfo.total_count -
          (realtimeInfo.total_anomaly + realtimeInfo.total_error),
      });
    }
  }, [realtimeInfo]);

  const onChange = (page, pageSize) => {
    setCurrent(page);
    setPageSize(pageSize);
  };

  const handleOnRowClick = (values) => {
    const { total_anomaly, total_error, total_count, id } = values;
    setStats({
      anomaly: total_anomaly,
      error: total_error,
      normal: total_count - (total_anomaly + total_error),
    });
    setCurrent(1);
    setPageSize(5);
    setSelectedId(id);
    setDisplayRealtime(false);
  };

  const { data: reportMsgs } = useGetAnomalyReportsbyidQuery(
    {
      id: selectedId,
      type: typeSelect,
      page,
      pageSize,
    },
    {
      refetchOnMountOrArgChange: true,
      skip: selectedId === "",
    }
  );

  useEffect(() => {
    setData({
      total: reportMsgs?.total || 0,
      messages: reportMsgs?.messages || [],
    });
  }, [reportMsgs]);

  useEffect(() => {
    setData({
      total: realtimeMessages?.total || 0,
      messages: realtimeMessages?.messages || [],
    });
  }, [realtimeMessages]);

  return (
    <>
      <RcResizeObserver
        key="resize-observer"
        onResize={(offset) => {
          setResponsive(offset.width < 596);
        }}
      >
        <ProCard
          headerBordered
          headStyle={{ flexWrap: "wrap" }}
          bordered
          title={clientName}
          split={responsive ? "horizontal" : "vertical"}
          extra={
            <Flex gap={10} style={{ flexWrap: "wrap" }}>
              <Button
                onClick={() => {
                  setModalType("detect");
                  setOpenAnomModel(true);
                }}
              >
                detect log file
              </Button>
              <Button
                onClick={() => {
                  setModalType("anomalies import");
                  setOpenAnomModel(true);
                }}
              >
                import Anomaly file
              </Button>
              <Button
                onClick={() => {
                  setModalType("normals import");
                  setOpenAnomModel(true);
                }}
              >
                import normal file
              </Button>
              <Button
                onClick={() => {
                  setModalType("knowledge backup");
                  setOpenAnomModel(true);
                }}
              >
                knowledge backup
              </Button>
              <Button
                onClick={() => {
                  setModalType("knowledge restore");
                  setOpenAnomModel(true);
                }}
              >
                knowledge restore
              </Button>
              <Button onClick={() => setOpenAddModels(true)}>Add</Button>
            </Flex>
          }
        >
          <ProCard split="horizontal" colSpan={responsive ? "100%" : "60%"}>
            <ProCard split={responsive ? "horizontal" : "vertical"}>
              <StatisticCard
                statistic={{
                  title: (
                    <Flex justify="space-between">
                      <Typography.Title level={5}>Model using</Typography.Title>
                      <AnomalySettingsPop
                        clientName={clientName}
                        title="model setting"
                        settingType="model"
                      />
                    </Flex>
                  ),
                  value: `${settings.model}`,
                }}
              />
              <StatisticCard
                statistic={{
                  title: (
                    <Flex justify="space-between">
                      <Typography.Title level={5}>
                        Pull interval
                      </Typography.Title>
                      <AnomalySettingsPop
                        clientName={clientName}
                        title="Pull interval"
                        settingType="pull_interval"
                      />
                    </Flex>
                  ),
                  value: `${settings.pull_interval_mins} min`,
                }}
              />
              <StatisticCard
                statistic={{
                  title: (
                    <Flex justify="space-between">
                      <Typography.Title level={5}>Auto detect</Typography.Title>
                      <AnomalySettingsPop
                        clientName={clientName}
                        title="auto detect"
                        settingType="auto_detect"
                      />
                    </Flex>
                  ),
                  value: settings.detect ? "YES" : "NO",
                }}
              />
              <StatisticCard
                statistic={{
                  title: (
                    <Flex justify="space-between">
                      <Typography.Title level={5}>Score</Typography.Title>
                      <AnomalySettingsPop
                        clientName={clientName}
                        title="Score setting"
                        settingType="distance"
                      />
                    </Flex>
                  ),
                  value: settings.score,
                }}
              />
            </ProCard>

            <AnomalyRealtimeStats clientName={clientName} />

            <ProCard
              bodyStyle={{ padding: 0 }}
              split={responsive ? "horizontal" : "vertical"}
            >
              <ProCard checked={settings.model === "open-ai"}>
                <ProDescriptions
                  column={1}
                  title="open-ai settings"
                  extra={
                    <AnomalySettingsPop
                      clientName={clientName}
                      title="api key"
                      settingType="api_key"
                    />
                  }
                >
                  <ProDescriptions.Item
                    valueType="text"
                    ellipsis
                    label="Demo api key"
                  >
                    {settings?.openai_settings?.is_demo_api_key ? "YES" : "NO"}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item
                    valueType="text"
                    ellipsis
                    label="Api key"
                  >
                    {settings?.openai_settings?.api_key}
                  </ProDescriptions.Item>
                </ProDescriptions>
              </ProCard>
              <ProCard checked={settings.model === "ollama"}>
                <ProDescriptions
                  column={2}
                  title="ollama settings"
                  extra={
                    <AnomalySettingsPop
                      clientName={clientName}
                      title="Ollama settings"
                      settingType="ollama"
                    />
                  }
                >
                  <ProDescriptions.Item valueType="text" ellipsis label="Host">
                    {settings?.ollama_settings?.host}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item valueType="text" ellipsis label="Model">
                    {settings?.ollama_settings?.model}
                  </ProDescriptions.Item>
                  <ProDescriptions.Item valueType="text" ellipsis label="Port">
                    {settings?.ollama_settings?.port}
                  </ProDescriptions.Item>
                </ProDescriptions>
              </ProCard>
            </ProCard>
            <ProCard>
              <LogHistory client={clientName} onRowclick={handleOnRowClick} />
            </ProCard>
          </ProCard>
          <ProCard
            bodyStyle={{ padding: 0 }}
            split="horizontal"
            bordered={false}
          >
            <Flex vertical>
              <Flex>
                <Switch
                  checked={displayRealtime}
                  onChange={() => setDisplayRealtime((prev) => !prev)}
                  checkedChildren="Realtime"
                  unCheckedChildren="Realtime"
                  style={{
                    width: "auto",
                    margin: "5px",
                    alignSelf: "flex-start",
                  }}
                />
                <Button
                  onClick={handleClearRealtimeReport}
                  disabled={!displayRealtime}
                  size="small"
                  style={{ margin: "5px" }}
                >
                  Clear
                </Button>
              </Flex>
              <StatisticCard
                bodyStyle={{ padding: 0 }}
                title={`Detect Reports`}
                chart={
                  <LogPieChart
                    series={[
                      stats?.error || 0,
                      stats?.anomaly || 0,
                      stats?.normal || 0,
                    ]}
                  />
                }
              />
              <StatisticCard
                bodyStyle={{ padding: 0 }}
                headStyle={{ flexWrap: "wrap" }}
                title={`Detect message list`}
                extra={
                  <Radio.Group
                    options={options}
                    onChange={({ target: { value } }) => {
                      setCurrent(1);
                      setPageSize(5);
                      setTypeSelect(value);
                    }}
                    value={typeSelect}
                    optionType="button"
                  />
                }
                chart={
                  <List
                    pagination={{
                      position: "bottom",
                      align: "center",
                      style: { flexWrap: "wrap" },
                      pageSize: pageSize,
                      current: page,
                      onChange,
                      total: data?.total || 0,
                    }}
                    size="small"
                    dataSource={data?.messages || []}
                    renderItem={(item) => (
                      <List.Item style={{ wordWrap: "anywhere" }}>
                        {item.state === "normal" && (
                          <span>
                            <Tag color="green">[NOR]</Tag> {item.message}
                          </span>
                        )}
                        {item.state === "anomaly" && (
                          <span>
                            <Tag color="#FF9800">[ANO]</Tag> {item.message}
                          </span>
                        )}
                        {item.state === "error" && (
                          <span>
                            <Tag color="#E91E63">[ERR]</Tag> {item.message}
                          </span>
                        )}
                      </List.Item>
                    )}
                  />
                }
              />
            </Flex>
          </ProCard>
        </ProCard>
      </RcResizeObserver>
      <AnomalyImportModel
        open={openAnomModel}
        onClose={() => {
          setOpenAnomModel(false);
          setModalType("detect");
        }}
        svcId={clientName}
        type={modalType}
      />
      <AnomalyAddModel
        open={openAddModels}
        onClose={() => setOpenAddModels(false)}
        svcId={clientName}
      />
    </>
  );
};

export default AnomalySettingStats;

// eslint-disable-next-line react/prop-types
export const AnomalyImportModel = ({ open, onClose, svcId, type }) => {
  const [form] = Form.useForm();
  const { notification } = App.useApp();
  const [handleImportFile, { isLoading }] = useImportFileMutation();

  const [handleAnomBackup, { isLoading: isBackupLoading }] =
    useAnomalyBackupMutation();

  const HandleImport = async (data) => {
    try {
      await handleImportFile({ url: data, svc: svcId, impType: type }).unwrap();
      notification.success({
        message: `successfully ${type} command sent`,
      });
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    } finally {
      form.setFieldsValue({ importUrls: "" });
      onClose();
    }
  };

  const HandleBackup = async (data) => {
    try {
      await handleAnomBackup({
        url: data.url,
        filename: data.filename,
        svc: svcId,
        impType: type,
      }).unwrap();
      notification.success({
        message: `successfully ${type} command sent`,
      });
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    } finally {
      form.setFieldsValue({ importUrl: "", filename: "" });
      onClose();
    }
  };

  return (
    <Modal
      open={open}
      width={700}
      forceRender
      maskClosable={false}
      title={`${type} file for ${svcId}`}
      okText="ok"
      cancelText="CANCEL"
      confirmLoading={isLoading || isBackupLoading}
      onCancel={() => {
        form.setFieldsValue({ importUrl: "", filename: "" });
        onClose();
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            if (type === "knowledge backup") {
              HandleBackup({
                url: values?.importUrl,
                filename: values?.filename,
              });
            } else {
              HandleImport(values?.importUrl);
            }
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form form={form} layout="vertical" name="form_in_modal_anomaly">
        {type === "knowledge backup" && (
          <Form.Item
            name="filename"
            label="Filename"
            rules={[
              {
                required: true,
                message: "Please input the filename!",
              },
            ]}
          >
            <Input />
          </Form.Item>
        )}
        <Form.Item
          name="importUrl"
          label="File URL"
          rules={[
            {
              required: true,
              message: "Please input the file URL!",
            },
            {
              type: "url",
              message: "please input the url type",
            },
          ]}
        >
          <Input.TextArea autoSize={{ minRows: 3, maxRows: 10 }} />
        </Form.Item>
      </Form>
    </Modal>
  );
};
// eslint-disable-next-line react/prop-types
export const AnomalyAddModel = ({ open, onClose, svcId }) => {
  const [form] = Form.useForm();
  const { notification } = App.useApp();
  const [handleAddAnomaly, { isLoading }] = useAddAnomalyMutation();

  const HandleAddAnomaly = async (message, type) => {
    try {
      await handleAddAnomaly({
        messages: message.split("\n"),
        type,
        client: svcId,
      }).unwrap();
      notification.success({
        message: "successfully added messages!",
      });
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    } finally {
      form.setFieldsValue({ message: "", type: "anomalies" });
      onClose();
    }
  };

  return (
    <Modal
      open={open}
      width={700}
      forceRender
      maskClosable={false}
      title={`Add anomaly/normal for ${svcId}`}
      okText="Add"
      cancelText="CANCEL"
      confirmLoading={isLoading}
      onCancel={() => {
        form.setFieldsValue({ message: "", type: "anomalies" });
        onClose();
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            HandleAddAnomaly(values?.message, values?.type);
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form form={form} layout="vertical" name="form_in_modal_add_idp_rule">
        <Form.Item
          name="type"
          label="Type"
          rules={[
            {
              required: true,
              message: "Please select the type!",
            },
          ]}
        >
          <Radio.Group
            options={[
              {
                label: "Normal",
                value: "normals",
              },
              {
                label: "Anomaly",
                value: "anomalies",
              },
            ]}
            optionType="button"
          />
        </Form.Item>
        <Form.Item
          name="message"
          label="Message"
          rules={[
            {
              required: true,
              message: "Please input the message!",
            },
          ]}
        >
          <Input.TextArea autoSize={{ minRows: 4, maxRows: 10 }} />
        </Form.Item>
      </Form>
    </Modal>
  );
};
