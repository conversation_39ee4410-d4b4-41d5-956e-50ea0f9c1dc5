import { api } from "./api";

export const aiassistApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getLLMSettings: builder.query({
      query: () => `api/v1/ai-assist/llm-settings`,
      providesTags: ["ai-assist-llm-settings"],
    }),
    setLLMSettings: builder.mutation({
      query: (data) => ({
        url: `api/v1/ai-assist/llm-settings`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["ai-assist-llm-settings"],
    }),
    toolCallPermit: builder.mutation({
      query: ({ sessionid, toolcallid }) => ({
        url: "api/v1/ai-assistant/session/tool-call-permit",
        method: "POST",
        body: { sessionid, toolcallid },
      }),
      invalidatesTags: (result, error, { sessionid }) => [
        { type: "ChatSession", id: sessionid },
      ],
    }),
  }),
});

export const {
  useGetLLMSettingsQuery,
  useSetLLMSettingsMutation,
  useToolCallPermitMutation,
} = aiassistApi;
