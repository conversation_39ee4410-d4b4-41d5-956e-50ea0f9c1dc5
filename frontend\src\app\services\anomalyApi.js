import { api } from "./api";

export const anomalyApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getAnomalyStatistics: builder.query({
      query: () => "api/v1/anomaly/statistics",
      providesTags: ["anomalies"],
    }),
    getAnomalyReportsbyid: builder.query({
      query: (params) =>
        // if params.realtime is true, get realtime
        `api/v1/anomaly/reports?id=${params.id}&type=${params.type}&page=${params.page}&pageSize=${params.pageSize}`,
      providesTags: ["anomalies"],
    }),
    getRealtimeReportWithType: builder.query({
      query: (params) =>
        `api/v1/anomaly/realtime/report/messages?anomsvc=${params.id}&type=${params.type}&page=${params.page}&pageSize=${params.pageSize}`,
      providesTags: ["anomalies"],
    }),
    getAnomalyReportsAll: builder.query({
      query: (params) =>
        `api/v1/anomaly/reports?id=${params.id}&page=${params.page}&pageSize=${params.pageSize}`,
      providesTags: ["anomalies"],
    }),
    getRealtimeReportAll: builder.query({
      query: (params) =>
        `api/v1/anomaly/realtime/report/messages?anomsvc=${params.id}&type=all&page=${params.page}&pageSize=${params.pageSize}`,
      providesTags: ["anomalies"],
    }),
    getAnomalyReportswithPaging: builder.query({
      query: (data) =>
        `api/v1/anomaly/reports/list?client=${data.client}&page=${data.page}&pageSize=${data.pageSize}`,
      providesTags: ["anomalies"],
    }),
    importFile: builder.mutation({
      query: (data) => ({
        url: `api/v1/commands`,
        method: "POST",
        body: JSON.stringify([
          {
            command: `anomaly ${data.impType} ${data.url}`,
            client: data.svc,
          },
        ]),
      }),
      invalidatesTags: ["anomalies"],
    }),
    anomalyBackup: builder.mutation({
      query: (data) => ({
        url: `api/v1/commands`,
        method: "POST",
        body: JSON.stringify([
          {
            command: `anomaly ${data.impType} ${data.url} ${data.filename}`,
            client: data.svc,
          },
        ]),
      }),
      invalidatesTags: ["anomalies"],
    }),
    anomalySettings: builder.mutation({
      query: (data) => ({
        url: `api/v1/commands`,
        method: "POST",
        body: JSON.stringify([
          {
            command: data.command,
            client: data.svc,
          },
        ]),
      }),
      invalidatesTags: ["anomalies"],
    }),
    deleteAnomalyReport: builder.mutation({
      query: (data) => ({
        url: `api/v1/anomaly/reports`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["anomalies"],
    }),
    addAnomaly: builder.mutation({
      query: (data) => ({
        url: `api/v1/anomaly/knowledge`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["anomalies"],
    }),
    // GET /clients
    getSettingsByClient: builder.query({
      query: () => `api/v1/clients`,
      providesTags: ["anomalies"],
    }),

    //POST /anomaly/realtime/settings
    settingRealtime: builder.mutation({
      query: (data) => ({
        url: `api/v1/anomaly/realtime/settings`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["anomaly-realtime"],
    }),
    fetchRealtimeSettings: builder.query({
      query: ({ anomsvc }) => ({
        url: `api/v1/anomaly/realtime/settings`,
        method: "GET",
        params: { anomsvc },
      }),
      providesTags: ["anomaly-realtime"],
    }),
    // Updated query to poll /api/v1/anomaly/realtime every 5 minutes with a dynamic client parameter
    getRealtimeData: builder.query({
      query: (client) => `api/v1/anomaly/realtime?client=${client}`,
      providesTags: ["anomaly-realtime"],
      pollingInterval: 300000, // 5 minutes in milliseconds
    }),
    getRealtimeReport: builder.query({
      query: (client) => `api/v1/anomaly/realtime/report?anomsvc=${client}`,
      providesTags: ["anomaly-realtime"],
    }),
    clearRealtimeReport: builder.mutation({
      query: (data) => ({
        url: `api/v1/anomaly/realtime/report/clear`,
        method: "POST",
        body: data,
      }),

      invalidatesTags: ["anomaly-realtime"],
    }),
  }),
});

export const {
  useGetAnomalyStatisticsQuery,
  useGetAnomalyReportsbyidQuery,
  useGetSettingsByClientQuery,
  useImportFileMutation,
  useAnomalyBackupMutation,
  useAnomalySettingsMutation,
  useGetAnomalyReportswithPagingQuery,
  useDeleteAnomalyReportMutation,
  useAddAnomalyMutation,
  useGetAnomalyReportsAllQuery,
  useSettingRealtimeMutation,
  useLazyFetchRealtimeSettingsQuery,
  useGetRealtimeDataQuery, // Get client's realtime statistics
  useGetRealtimeReportQuery, // Get client's realtime report
  useGetRealtimeReportAllQuery, // Get client's realtime report all messages
  useGetRealtimeReportWithTypeQuery, // Get client's realtime report with type
  useClearRealtimeReportMutation,
} = anomalyApi;
